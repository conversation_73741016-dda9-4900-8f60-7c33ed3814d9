package com.syos.test.service;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import static org.junit.Assert.*;

import com.syos.service.ReportService;
import com.syos.service.InventoryService;
import com.syos.service.BillingService;
import com.syos.model.*;
import com.syos.repository.*;
import com.syos.repository.impl.*;
import com.syos.util.FileUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * Comprehensive unit tests for ReportService
 * Tests all report generation functionality and edge cases
 */
public class ReportServiceTest {

    private ReportService reportService;
    private InventoryService inventoryService;
    private BillingService billingService;
    private ItemRepository itemRepository;
    private StockRepository stockRepository;
    private TransactionRepository transactionRepository;
    private BillRepository billRepository;

    @Before
    public void setUp() {
        // Use file-based repositories for testing
        itemRepository = new FileItemRepository();
        stockRepository = new FileStockRepository();
        transactionRepository = new FileTransactionRepository();
        billRepository = new FileBillRepository();
        UserRepository userRepository = new FileUserRepository();

        inventoryService = new InventoryService(itemRepository, stockRepository);
        billingService = new BillingService(itemRepository, transactionRepository, billRepository, inventoryService);
        reportService = new ReportService(itemRepository, stockRepository, transactionRepository, billRepository,
                inventoryService);

        // Clean up any existing test data
        cleanupTestData();

        // Set up test data
        setupTestData();
    }

    @After
    public void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        // Clean up test files
        String[] testItemCodes = { "RPT_I001", "RPT_I002", "RPT_I003", "RPT_I004", "RPT_I005" };
        for (String code : testItemCodes) {
            itemRepository.delete(code);
        }

        // Clean up generated report files
        String[] reportFiles = {
                "reports/full_stock_" + LocalDate.now() + ".txt",
                "reports/reshelf_" + LocalDate.now() + ".txt",
                "reports/reorder_" + LocalDate.now() + ".txt",
                "reports/daily_sales_" + LocalDate.now() + ".txt",
                "reports/bill_report_" + LocalDate.now() + ".txt"
        };

        for (String file : reportFiles) {
            FileUtil.deleteFile(file);
        }
    }

    private void setupTestData() {
        // Create test items
        Item item1 = new Item("RPT_I001", "Test Rice", 2.50, "Groceries");
        Item item2 = new Item("RPT_I002", "Test Flour", 1.80, "Groceries");
        Item item3 = new Item("RPT_I003", "Test Milk", 1.20, "Dairy");
        Item item4 = new Item("RPT_I004", "Test Bread", 1.00, "Bakery");
        Item item5 = new Item("RPT_I005", "Test Apples", 2.20, "Fruits");

        itemRepository.save(item1);
        itemRepository.save(item2);
        itemRepository.save(item3);
        itemRepository.save(item4);
        itemRepository.save(item5);

        // Create test stock batches with different scenarios
        LocalDate today = LocalDate.now();

        // Normal stock
        stockRepository
                .save(new StockBatch("RPT_SB001", item1, 25, today.minusDays(5), today.plusMonths(6), StockType.SHELF));
        stockRepository.save(
                new StockBatch("RPT_ST001", item1, 75, today.minusDays(10), today.plusMonths(8), StockType.STORAGE));

        // Low stock (needs reorder)
        stockRepository
                .save(new StockBatch("RPT_SB002", item2, 5, today.minusDays(3), today.plusMonths(4), StockType.SHELF));
        stockRepository.save(
                new StockBatch("RPT_ST002", item2, 10, today.minusDays(8), today.plusMonths(6), StockType.STORAGE));

        // Expiring soon stock (needs reshelf)
        stockRepository
                .save(new StockBatch("RPT_SB003", item3, 30, today.minusDays(2), today.plusDays(3), StockType.SHELF));
        stockRepository.save(
                new StockBatch("RPT_ST003", item3, 50, today.minusDays(7), today.plusMonths(2), StockType.STORAGE));

        // Expired stock
        stockRepository
                .save(new StockBatch("RPT_SB004", item4, 15, today.minusDays(15), today.minusDays(1), StockType.SHELF));

        // Online stock
        stockRepository.save(
                new StockBatch("RPT_ON001", item5, 40, today.minusDays(4), today.plusMonths(3), StockType.ONLINE));
    }

    // ========== FULL STOCK REPORT TESTS ==========

    @Test
    public void testGenerateFullStockReport() {
        String reportPath = reportService.generateFullStockReport();

        assertNotNull("Report path should not be null", reportPath);
        assertTrue("Report file should exist", FileUtil.fileExists(reportPath));

        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        assertFalse("Report should not be empty", reportLines.isEmpty());

        String reportContent = String.join("\n", reportLines);
        assertTrue("Report should contain header", reportContent.contains("FULL STOCK REPORT"));
        assertTrue("Report should contain test items", reportContent.contains("RPT_I001"));
        assertTrue("Report should contain stock types", reportContent.contains("SHELF"));
        assertTrue("Report should contain quantities", reportContent.contains("25"));
    }

    @Test
    public void testFullStockReportStructure() {
        String reportPath = reportService.generateFullStockReport();
        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);

        boolean hasHeader = false;
        boolean hasColumnHeaders = false;
        boolean hasSeparator = false;
        boolean hasData = false;

        for (String line : reportLines) {
            if (line.contains("FULL STOCK REPORT"))
                hasHeader = true;
            if (line.contains("Code") && line.contains("Name") && line.contains("Category"))
                hasColumnHeaders = true;
            if (line.contains("=====") || line.contains("-----"))
                hasSeparator = true;
            if (line.contains("RPT_I"))
                hasData = true;
        }

        assertTrue("Report should have header", hasHeader);
        assertTrue("Report should have column headers", hasColumnHeaders);
        assertTrue("Report should have separator lines", hasSeparator);
        assertTrue("Report should have data", hasData);
    }

    // ========== RESHELF REPORT TESTS ==========

    @Test
    public void testGenerateReshelfReport() {
        String reportPath = reportService.generateReshelfReport();

        assertNotNull("Report path should not be null", reportPath);
        assertTrue("Report file should exist", FileUtil.fileExists(reportPath));

        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        assertFalse("Report should not be empty", reportLines.isEmpty());

        String reportContent = String.join("\n", reportLines);
        assertTrue("Report should contain header", reportContent.contains("RESHELF REPORT"));
        assertTrue("Report should contain expiring items", reportContent.contains("RPT_I003"));
    }

    @Test
    public void testReshelfReportContainsExpiringSoonItems() {
        String reportPath = reportService.generateReshelfReport();
        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        String reportContent = String.join("\n", reportLines);

        // Should contain item that expires in 3 days (RPT_I003)
        assertTrue("Should contain expiring soon item", reportContent.contains("RPT_I003"));
        assertTrue("Should contain Test Milk", reportContent.contains("Test Milk"));
    }

    // ========== REORDER REPORT TESTS ==========

    @Test
    public void testGenerateReorderReport() {
        String reportPath = reportService.generateReorderReport();

        assertNotNull("Report path should not be null", reportPath);
        assertTrue("Report file should exist", FileUtil.fileExists(reportPath));

        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        assertFalse("Report should not be empty", reportLines.isEmpty());

        String reportContent = String.join("\n", reportLines);
        assertTrue("Report should contain header", reportContent.contains("REORDER REPORT"));
        assertTrue("Report should contain low stock items", reportContent.contains("RPT_I002"));
    }

    @Test
    public void testReorderReportContainsLowStockItems() {
        String reportPath = reportService.generateReorderReport();
        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        String reportContent = String.join("\n", reportLines);

        // Should contain item with low stock (RPT_I002 has only 15 total)
        assertTrue("Should contain low stock item", reportContent.contains("RPT_I002"));
        assertTrue("Should contain Test Flour", reportContent.contains("Test Flour"));
    }

    // ========== DAILY SALES REPORT TESTS ==========

    @Test
    public void testGenerateDailySalesReport() {
        // Create some test transactions first
        createTestTransactions();

        String reportPath = reportService.generateDailySalesReport(LocalDate.now());

        assertNotNull("Report path should not be null", reportPath);
        assertTrue("Report file should exist", FileUtil.fileExists(reportPath));

        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        assertFalse("Report should not be empty", reportLines.isEmpty());

        String reportContent = String.join("\n", reportLines);
        assertTrue("Report should contain header", reportContent.contains("Daily Sales Report"));
    }

    @Test
    public void testDailySalesReportWithNoTransactions() {
        // Generate report for a date with no transactions
        LocalDate futureDate = LocalDate.now().plusDays(10);
        String reportPath = reportService.generateDailySalesReport(futureDate);

        assertNotNull("Report path should not be null", reportPath);
        assertTrue("Report file should exist", FileUtil.fileExists(reportPath));

        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        String reportContent = String.join("\n", reportLines);
        assertTrue("Report should indicate no sales",
                reportContent.contains("No sales") || reportContent.contains("Total Revenue: $0.00"));
    }

    // ========== BILL REPORT TESTS ==========

    @Test
    public void testGenerateBillReport() {
        // Create some test bills first
        createTestTransactions();

        String reportPath = reportService.generateBillReport();

        assertNotNull("Report path should not be null", reportPath);
        assertTrue("Report file should exist", FileUtil.fileExists(reportPath));

        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        assertFalse("Report should not be empty", reportLines.isEmpty());

        String reportContent = String.join("\n", reportLines);
        assertTrue("Report should contain header", reportContent.contains("Bill Report"));
    }

    // ========== HELPER METHODS ==========

    private void createTestTransactions() {
        try {
            // Create a simple transaction for testing
            Transaction transaction = new Transaction();
            transaction.setTransactionId("RPT_T001");
            transaction.setType(TransactionType.IN_STORE);
            transaction.setDateTime(LocalDateTime.now());
            transaction.setTotalAmount(10.00);
            transaction.setFinalAmount(10.00);

            // Add some items to the transaction
            List<CartItem> items = new ArrayList<>();
            Item item1 = itemRepository.findByCode("RPT_I001").orElse(null);
            if (item1 != null) {
                items.add(new CartItem(item1, 2));
            }
            transaction.setItems(items);

            transactionRepository.save(transaction);

            // Create a bill for the transaction
            Bill bill = new Bill("RPT_B001", transaction);
            billRepository.save(bill);

        } catch (Exception e) {
            // Ignore errors in test data creation
            System.err.println("Error creating test transactions: " + e.getMessage());
        }
    }

    // ========== EDGE CASE TESTS ==========

    @Test
    public void testReportServiceWithEmptyInventory() {
        // Clean up all test data
        cleanupTestData();

        String reportPath = reportService.generateFullStockReport();

        assertNotNull("Report path should not be null", reportPath);
        assertTrue("Report file should exist", FileUtil.fileExists(reportPath));

        List<String> reportLines = FileUtil.readLinesFromFile(reportPath);
        String reportContent = String.join("\n", reportLines);
        assertTrue("Report should contain header even when empty", reportContent.contains("FULL STOCK REPORT"));
    }

    @Test
    public void testReportServiceWithNullDate() {
        try {
            reportService.generateDailySalesReport(null);
            fail("Should throw exception for null date");
        } catch (Exception e) {
            // Expected
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void testReportServiceWithNullRepository() {
        new ReportService(null, stockRepository, transactionRepository, billRepository, inventoryService);
    }
}
