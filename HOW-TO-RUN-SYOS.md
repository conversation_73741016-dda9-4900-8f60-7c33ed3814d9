# How to Run SYOS Application

## 🚀 Quick Start Options

### Option 1: Run with MySQL (Recommended)
```cmd
run-mysql.bat
```

### Option 2: Run with Default Settings
```cmd
run.bat
```

### Option 3: Run with File Storage Only
```cmd
run-file.bat
```

## 📋 Detailed Instructions

### Method 1: MySQL Database (Best for Production)

**Prerequisites:**
- MySQL installed and running
- Database configured (run `quick-mysql-setup.bat` if not done)

**Steps:**
1. Open Command Prompt in your project directory
2. Run the MySQL version:
   ```cmd
   run-mysql.bat
   ```

**What it does:**
- Compiles all Java files
- Connects to MySQL database
- Loads/creates database tables
- Starts the application with full database functionality

### Method 2: Default Run (Auto-detects Storage)

**Steps:**
1. Open Command Prompt in your project directory
2. Run the default script:
   ```cmd
   run.bat
   ```

**What it does:**
- Tries to connect to MySQL first
- Falls back to CSV files if MySQL unavailable
- Automatically handles storage type selection

### Method 3: File Storage Only (Development/Testing)

**Steps:**
1. Open Command Prompt in your project directory
2. Run the file-only version:
   ```cmd
   run-file.bat
   ```

**What it does:**
- Uses CSV files for data storage
- No database required
- Good for testing and development

## 🔧 Manual Compilation and Run

If you prefer to compile and run manually:

### Step 1: Compile
```cmd
# Create bin directory
mkdir bin

# Compile with MySQL support
javac -d bin -cp "lib/mysql-connector-java-5.1.49.jar" com/syos/config/*.java com/syos/util/*.java com/syos/model/*.java com/syos/repository/*.java com/syos/repository/impl/*.java com/syos/factory/*.java com/syos/service/*.java com/syos/ui/*.java com/syos/*.java
```

### Step 2: Run
```cmd
# Run with MySQL
java -cp "bin;lib/mysql-connector-java-5.1.49.jar" com.syos.SyosApplication

# Or run with file storage only
java -cp "bin" -Dstorage.type=file com.syos.SyosApplication
```

## ⚙️ Configuration Options

### Environment Variables (via .env file)
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=syos_db
DB_USERNAME=root
DB_PASSWORD=your_password
```

### System Properties (command line)
```cmd
java -cp "bin;lib/mysql-connector-java-5.1.49.jar" ^
     -Ddb.host=localhost ^
     -Ddb.port=3306 ^
     -Ddb.name=syos_db ^
     -Ddb.username=root ^
     -Ddb.password=your_password ^
     com.syos.SyosApplication
```

### Storage Type Override
```cmd
# Force MySQL storage
java -cp "bin;lib/mysql-connector-java-5.1.49.jar" -Dstorage.type=mysql com.syos.SyosApplication

# Force file storage
java -cp "bin" -Dstorage.type=file com.syos.SyosApplication
```

## 🎯 Expected Output

### Successful MySQL Startup
```
Starting SYOS with MySQL Database...
Compiling application...
Running SYOS...
Starting Synex Outlet Store (SYOS) System...
Using MySQL database storage...
Loaded database configuration from .env file
Database initialized successfully
Sample data loaded successfully.

===== SYNEX OUTLET STORE (SYOS) =====
1. Billing Operations
2. Inventory Management
3. Reports
4. User Management
5. Exit
Enter your choice:
```

### Successful File Storage Startup
```
Starting SYOS with MySQL Database...
Compiling application...
Running SYOS...
Starting Synex Outlet Store (SYOS) System...
Using file-based storage...
Data files loaded successfully.

===== SYNEX OUTLET STORE (SYOS) =====
1. Billing Operations
2. Inventory Management
3. Reports
4. User Management
5. Exit
Enter your choice:
```

## 🐛 Troubleshooting

### Compilation Errors
```
Compilation failed!
```
**Solutions:**
- Check if all Java files are present
- Verify JDBC driver exists in lib/ folder
- Ensure Java is properly installed

### MySQL Connection Errors
```
Cannot connect to MySQL database
```
**Solutions:**
- Run `verify-mysql-setup.bat` to check configuration
- Ensure MySQL service is running: `net start mysql80`
- Check .env file has correct credentials
- See `MYSQL-TROUBLESHOOTING.md` for detailed help

### "Class not found" Errors
```
Error: Could not find or load main class com.syos.SyosApplication
```
**Solutions:**
- Make sure compilation was successful
- Check classpath includes bin directory
- Verify main class exists in com/syos/SyosApplication.java

## 📁 File Structure Check

Before running, ensure you have:
```
project/
├── run.bat                    # Main run script
├── run-mysql.bat             # MySQL-specific run script
├── run-file.bat              # File storage run script
├── .env                      # Database configuration
├── lib/
│   └── mysql-connector-*.jar # JDBC driver
├── com/syos/
│   ├── SyosApplication.java  # Main class
│   └── ...                   # Other source files
└── data/                     # CSV data files (backup)
```

## 🎮 Using the Application

Once started, you'll see the main menu:

```
===== SYNEX OUTLET STORE (SYOS) =====
1. Billing Operations
2. Inventory Management
3. Reports
4. User Management
5. Exit
Enter your choice:
```

### Navigation:
- **Enter numbers (1-5)** to select menu options
- **Follow prompts** for each operation
- **Enter 5** to exit the application

### Key Features:
1. **Billing Operations** - Create bills, process sales
2. **Inventory Management** - Add/update items, manage stock
3. **Reports** - Generate various business reports
4. **User Management** - Manage system users
5. **Exit** - Close the application

## 🔄 Restarting the Application

To restart:
1. **Press Ctrl+C** to stop the current session
2. **Run the script again** (run.bat, run-mysql.bat, etc.)

## 💾 Data Persistence

- **MySQL Mode**: Data saved to database permanently
- **File Mode**: Data saved to CSV files in data/ folder
- **Mixed Mode**: Falls back gracefully between storage types

## 📞 Need Help?

1. **Check setup**: Run `verify-mysql-setup.bat`
2. **Read troubleshooting**: `MYSQL-TROUBLESHOOTING.md`
3. **Review configuration**: Check `.env` file
4. **Test connection**: Use MySQL command line tools

---

**Ready to start?** Just run `run-mysql.bat` and begin using your SYOS application!
