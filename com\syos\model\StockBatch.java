package com.syos.model;

import java.time.LocalDate;

/**
 * Represents a batch of items in stock.
 * Each batch has a unique ID, associated item, quantity, received date, and
 * expiry date.
 */
public class StockBatch {
    private String batchId;
    private Item item;
    private int quantity;
    private LocalDate receivedDate;
    private LocalDate expiryDate;
    private StockType stockType; // SHELF, STORAGE, ONLINE

    public StockBatch() {
    }

    public StockBatch(String batchId, Item item, int quantity, LocalDate receivedDate,
            LocalDate expiryDate, StockType stockType) {
        this.batchId = batchId;
        this.item = item;
        this.quantity = quantity;
        this.receivedDate = receivedDate;
        this.expiryDate = expiryDate;
        this.stockType = stockType;
    }

    // Constructor for tests that takes itemCode instead of Item object
    public StockBatch(String batchId, String itemCode, int quantity, LocalDate receivedDate,
            LocalDate expiryDate, StockType stockType) {
        if (batchId == null || batchId.trim().isEmpty()) {
            throw new IllegalArgumentException("Batch ID cannot be null or empty");
        }
        if (quantity < 0) {
            throw new IllegalArgumentException("Quantity cannot be negative");
        }
        if (receivedDate != null && expiryDate != null && receivedDate.isAfter(expiryDate)) {
            throw new IllegalArgumentException("Received date cannot be after expiry date");
        }

        this.batchId = batchId;
        this.item = new Item(); // Create empty item with just the code
        this.item.setCode(itemCode);
        this.quantity = quantity;
        this.receivedDate = receivedDate;
        this.expiryDate = expiryDate;
        this.stockType = stockType;
    }

    // Getters and Setters
    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Item getItem() {
        return item;
    }

    public void setItem(Item item) {
        this.item = item;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public LocalDate getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDate receivedDate) {
        this.receivedDate = receivedDate;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
    }

    public StockType getStockType() {
        return stockType;
    }

    public void setStockType(StockType stockType) {
        this.stockType = stockType;
    }

    // Helper method to get item code
    public String getItemCode() {
        return item != null ? item.getCode() : null;
    }

    /**
     * Decreases the quantity of items in this batch.
     * 
     * @param amount The amount to decrease
     * @return true if successful, false if not enough stock
     */
    public boolean decreaseQuantity(int amount) {
        if (amount <= 0 || amount > quantity) {
            return false;
        }
        quantity -= amount;
        return true;
    }

    /**
     * Increases the quantity of items in this batch.
     * 
     * @param amount The amount to increase
     */
    public void increaseQuantity(int amount) {
        if (amount > 0) {
            quantity += amount;
        }
    }

    /**
     * Checks if this batch is expired.
     * 
     * @return true if expired, false otherwise
     */
    public boolean isExpired() {
        return LocalDate.now().isAfter(expiryDate);
    }

    /**
     * Checks if this batch is expiring soon (within 5 days).
     *
     * @return true if expiring soon, false otherwise
     */
    public boolean isExpiringSoon() {
        long daysUntilExpiry = daysUntilExpiry();
        return daysUntilExpiry >= 0 && daysUntilExpiry <= 5;
    }

    /**
     * Calculates days until expiry.
     *
     * @return days until expiry, negative if already expired
     */
    public long daysUntilExpiry() {
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDate.now(), expiryDate);
    }

    @Override
    public String toString() {
        return "StockBatch{" +
                "batchId='" + batchId + '\'' +
                ", item=" + item +
                ", quantity=" + quantity +
                ", receivedDate=" + receivedDate +
                ", expiryDate=" + expiryDate +
                ", stockType=" + stockType +
                '}';
    }
}
