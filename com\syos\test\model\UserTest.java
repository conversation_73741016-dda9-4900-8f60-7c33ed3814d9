package com.syos.test.model;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.model.User;

/**
 * Unit tests for User model class
 */
public class UserTest {
    
    private User user;
    
    @Before
    public void setUp() {
        user = new User("U001", "testuser", "password123", "Test User", 
            "<EMAIL>", "123 Test Street", "1234567890", true);
    }
    
    @Test
    public void testUserCreation() {
        assertNotNull("User should not be null", user);
        assertEquals("User ID should match", "U001", user.getId());
        assertEquals("Username should match", "testuser", user.getUsername());
        assertEquals("Full name should match", "Test User", user.getFullName());
        assertEquals("Email should match", "<EMAIL>", user.getEmail());
        assertTrue("Should be online customer", user.isOnlineCustomer());
    }
    
    @Test
    public void testPasswordValidation() {
        assertTrue("Password should be valid", user.validatePassword("password123"));
        assertFalse("Wrong password should be invalid", user.validatePassword("wrongpassword"));
    }
    
    @Test
    public void testEmailValidation() {
        assertTrue("Valid email should pass", User.isValidEmail("<EMAIL>"));
        assertTrue("Valid email should pass", User.isValidEmail("<EMAIL>"));
        assertFalse("Invalid email should fail", User.isValidEmail("invalid-email"));
        assertFalse("Invalid email should fail", User.isValidEmail("@domain.com"));
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidUserId() {
        new User(null, "testuser", "password", "Test User", 
            "<EMAIL>", "123 Test Street", "1234567890", true);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidEmail() {
        new User("U002", "testuser", "password", "Test User", 
            "invalid-email", "123 Test Street", "1234567890", true);
    }
}
