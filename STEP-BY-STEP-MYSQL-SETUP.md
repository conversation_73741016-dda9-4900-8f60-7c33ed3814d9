# Step-by-Step MySQL Setup Guide for SYOS

Follow these steps in order to set up MySQL for your SYOS application.

## 🚀 Quick Start (Recommended)

If you already have MySQL installed:

1. **Run the quick setup script:**
   ```cmd
   quick-mysql-setup.bat
   ```

2. **Verify the setup:**
   ```cmd
   verify-mysql-setup.bat
   ```

3. **Run the application:**
   ```cmd
   run-mysql.bat
   ```

If everything works, you're done! If not, follow the detailed steps below.

## 📋 Detailed Setup Steps

### Step 1: Install MySQL Server

#### Option A: MySQL Installer (Easiest)
1. Download MySQL Installer from: https://dev.mysql.com/downloads/installer/
2. Run the installer as administrator
3. Choose "Developer Default" setup
4. Set root password to `root` (or remember your password)
5. Complete the installation

#### Option B: Manual Installation
See `MYSQL-COMPLETE-SETUP-GUIDE.md` for detailed instructions.

### Step 2: Verify MySQL Installation

Open Command Prompt as Administrator and test:
```cmd
mysql --version
mysql -u root -p
```
Enter your password when prompted. You should see the MySQL prompt: `mysql>`

Type `exit;` to quit MySQL.

### Step 3: Create Database Configuration

The `.env` file should already exist in your project. If not, create it:

```env
# Database Connection Settings
DB_HOST=localhost
DB_PORT=3306
DB_NAME=syos_db

# Database Credentials - UPDATE THESE!
DB_USERNAME=root
DB_PASSWORD=root

# Connection Settings
ENABLE_SSL=false
ALLOW_PUBLIC_KEY_RETRIEVAL=true
```

**Important:** Update `DB_PASSWORD` with your actual MySQL root password!

### Step 4: Create Database and Tables

#### Option A: Automated Setup
```cmd
quick-mysql-setup.bat
```

#### Option B: Manual Setup
```cmd
# Connect to MySQL
mysql -u root -p

# Create database
CREATE DATABASE syos_db;
exit;

# Setup tables
mysql -u root -p syos_db < database\setup.sql

# Load sample data (optional)
mysql -u root -p syos_db < database\sample_data.sql
```

### Step 5: Verify Setup

Run the verification script:
```cmd
verify-mysql-setup.bat
```

This will check:
- ✅ MySQL installation
- ✅ .env configuration file
- ✅ JDBC driver
- ✅ Database scripts
- ✅ MySQL service status
- ✅ Database connection
- ✅ Database and tables

### Step 6: Test the Application

Run SYOS with MySQL:
```cmd
run-mysql.bat
```

You should see:
```
Starting SYOS with MySQL Database...
Compiling application...
Running SYOS...
Starting Synex Outlet Store (SYOS) System...
Using MySQL database storage...
Loaded database configuration from .env file
Database initialized successfully
Sample data loaded successfully.

===== SYNEX OUTLET STORE (SYOS) =====
1. Billing Operations
2. Inventory Management
3. Reports
4. User Management
5. Exit
Enter your choice:
```

## 🔧 Configuration Options

### Environment Variables Priority

The application loads configuration in this order:
1. **System Properties** (highest priority)
2. **.env file** (medium priority)
3. **Default values** (lowest priority)

### System Properties Override

You can override .env settings using system properties:
```cmd
java -Ddb.username=myuser -Ddb.password=mypass -cp "lib/*;." com.syos.SyosApplication
```

### Multiple Environment Support

Create different .env files for different environments:
- `.env.development`
- `.env.production`
- `.env.test`

Copy the appropriate one to `.env` when needed.

## 🐛 Troubleshooting

### Common Issues

1. **"Access denied for user 'root'@'localhost'"**
   - Check password in .env file
   - Verify MySQL user exists
   - See `MYSQL-TROUBLESHOOTING.md` for password reset

2. **"Can't connect to MySQL server"**
   - Check if MySQL service is running: `net start mysql80`
   - Verify port 3306 is not blocked
   - Check Windows Firewall settings

3. **"Unknown database 'syos_db'"**
   - Create database: `mysql -u root -p -e "CREATE DATABASE syos_db;"`
   - Run setup script: `mysql -u root -p syos_db < database\setup.sql`

4. **Application uses CSV files instead of MySQL**
   - Check .env file exists and has correct settings
   - Verify database connection works
   - Restart application after changing .env

### Getting Help

1. **Run verification script:** `verify-mysql-setup.bat`
2. **Check troubleshooting guide:** `MYSQL-TROUBLESHOOTING.md`
3. **Review complete setup guide:** `MYSQL-COMPLETE-SETUP-GUIDE.md`

## 📁 File Structure

After setup, you should have:
```
project/
├── .env                           # Database configuration
├── quick-mysql-setup.bat          # Automated setup script
├── verify-mysql-setup.bat         # Verification script
├── run-mysql.bat                  # Run with MySQL
├── database/
│   ├── setup.sql                  # Database schema
│   └── sample_data.sql             # Sample data
├── lib/
│   └── mysql-connector-java-*.jar # JDBC driver
└── com/syos/config/
    └── DatabaseConfig.java        # Configuration class
```

## ✅ Success Indicators

You know the setup is working when:

1. **Verification script passes all checks**
2. **Application starts without MySQL errors**
3. **Data persists between application restarts**
4. **You can see data in MySQL:**
   ```cmd
   mysql -u root -p syos_db -e "SELECT * FROM items LIMIT 5;"
   ```

## 🎯 Next Steps

After successful setup:

1. **Test all SYOS features** to ensure they work with MySQL
2. **Create regular database backups**
3. **Consider creating a dedicated MySQL user** (instead of root)
4. **Review security settings** for production use
5. **Monitor application logs** for any database issues

## 📞 Support

If you encounter issues:
1. Run `verify-mysql-setup.bat` first
2. Check `MYSQL-TROUBLESHOOTING.md`
3. Review MySQL error logs
4. Check SYOS console output for error messages

Remember: The application will fall back to CSV files if MySQL is not available, so it will always run even if database setup fails.
