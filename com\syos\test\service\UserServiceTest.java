package com.syos.test.service;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.service.UserService;
import com.syos.model.User;
import com.syos.factory.ServiceFactory;

/**
 * Unit tests for UserService
 */
public class UserServiceTest {
    
    private UserService userService;
    
    @Before
    public void setUp() {
        System.setProperty("storage.type", "file");
        userService = ServiceFactory.createUserService();
    }
    
    @Test
    public void testRegisterUser() {
        User user = new User("TEST_U001", "testuser", "password123", "Test User", 
            "<EMAIL>", "123 Test St", "1234567890", true);
        
        boolean result = userService.registerUser(user);
        assertTrue("User should be registered successfully", result);
    }
    
    @Test
    public void testLoginValidUser() {
        User user = new User("TEST_U002", "logintest", "password123", "Login Test", 
            "<EMAIL>", "123 Test St", "1234567890", true);
        userService.registerUser(user);
        
        User loggedInUser = userService.login("logintest", "password123");
        assertNotNull("Login should succeed", loggedInUser);
        assertEquals("Username should match", "logintest", loggedInUser.getUsername());
    }
    
    @Test
    public void testLoginInvalidCredentials() {
        User loggedInUser = userService.login("nonexistent", "wrongpassword");
        assertNull("Login should fail for invalid credentials", loggedInUser);
    }
    
    // Additional test methods would go here...
}
