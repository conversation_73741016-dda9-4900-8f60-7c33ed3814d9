package com.syos.test.service;

import com.syos.model.User;
import com.syos.repository.UserRepository;
import com.syos.service.UserService;
import com.syos.test.base.BaseTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for the UserService class.
 * Uses Mockito to mock the repository dependencies.
 */
public class UserServiceTest extends BaseTest {

    @Mock
    private UserRepository userRepository;

    private UserService userService;

    /**
     * Sets up the test environment.
     * Initializes mocks and creates a UserService instance.
     */
    @Before
    public void setUpService() {
        MockitoAnnotations.openMocks(this);
        userService = new UserService(userRepository);
    }

    /**
     * Tests registering a new user.
     */
    @Test
    public void testRegisterUser() {
        // Create a test user
        User user = new User(
                "U001",
                "testuser",
                "password",
                "Test User",
                "<EMAIL>",
                "123 Test St",
                "123-456-7890",
                true);

        // Mock the repository behavior
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());
        when(userRepository.save(user)).thenReturn(user);

        // Call the service method
        User registeredUser = userService.registerUser(user);

        // Verify the result
        assertNotNull(registeredUser);
        assertEquals(user, registeredUser);

        // Verify the repository was called
        verify(userRepository).findByUsername("testuser");
        verify(userRepository).save(user);
    }

    /**
     * Tests registering a user with a duplicate username.
     */
    @Test(expected = IllegalArgumentException.class)
    public void testRegisterUserWithDuplicateUsername() {
        // Create a test user
        User user = new User(
                "U001",
                "testuser",
                "password",
                "Test User",
                "<EMAIL>",
                "123 Test St",
                "123-456-7890",
                true);

        // Mock the repository behavior
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(user));

        // Call the service method (should throw IllegalArgumentException)
        userService.registerUser(user);
    }

    /**
     * Tests authenticating a user.
     */
    @Test
    public void testAuthenticateUser() {
        // Create a test user
        User user = new User(
                "U001",
                "testuser",
                "password",
                "Test User",
                "<EMAIL>",
                "123 Test St",
                "123-456-7890",
                true);

        // Mock the repository behavior
        when(userRepository.authenticate("testuser", "password")).thenReturn(Optional.of(user));
        when(userRepository.authenticate("testuser", "wrongpassword")).thenReturn(Optional.empty());
        when(userRepository.authenticate("nonexistent", "password")).thenReturn(Optional.empty());

        // Call the service method
        Optional<User> authenticatedUser1 = userService.authenticateUser("testuser", "password");
        Optional<User> authenticatedUser2 = userService.authenticateUser("testuser", "wrongpassword");
        Optional<User> authenticatedUser3 = userService.authenticateUser("nonexistent", "password");

        // Verify the results
        assertTrue(authenticatedUser1.isPresent());
        assertEquals(user, authenticatedUser1.get());

        assertFalse(authenticatedUser2.isPresent());
        assertFalse(authenticatedUser3.isPresent());

        // Verify the repository was called
        verify(userRepository).authenticate("testuser", "password");
        verify(userRepository).authenticate("testuser", "wrongpassword");
        verify(userRepository).authenticate("nonexistent", "password");
    }

    /**
     * Tests getting a user by ID.
     */
    @Test
    public void testGetUserById() {
        // Create a test user
        User user = new User(
                "U001",
                "testuser",
                "password",
                "Test User",
                "<EMAIL>",
                "123 Test St",
                "123-456-7890",
                true);

        // Mock the repository behavior
        when(userRepository.findById("U001")).thenReturn(Optional.of(user));
        when(userRepository.findById("U999")).thenReturn(Optional.empty());

        // Call the service method
        Optional<User> foundUser1 = userService.getUserById("U001");
        Optional<User> foundUser2 = userService.getUserById("U999");

        // Verify the results
        assertTrue(foundUser1.isPresent());
        assertEquals(user, foundUser1.get());

        assertFalse(foundUser2.isPresent());

        // Verify the repository was called
        verify(userRepository).findById("U001");
        verify(userRepository).findById("U999");
    }

    /**
     * Tests getting a user by username.
     */
    @Test
    public void testGetUserByUsername() {
        // Create a test user
        User user = new User(
                "U001",
                "testuser",
                "password",
                "Test User",
                "<EMAIL>",
                "123 Test St",
                "123-456-7890",
                true);

        // Mock the repository behavior
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(user));
        when(userRepository.findByUsername("nonexistent")).thenReturn(Optional.empty());

        // Call the service method
        Optional<User> foundUser1 = userService.getUserByUsername("testuser");
        Optional<User> foundUser2 = userService.getUserByUsername("nonexistent");

        // Verify the results
        assertTrue(foundUser1.isPresent());
        assertEquals(user, foundUser1.get());

        assertFalse(foundUser2.isPresent());

        // Verify the repository was called
        verify(userRepository).findByUsername("testuser");
        verify(userRepository).findByUsername("nonexistent");
    }

    /**
     * Tests getting all users.
     */
    @Test
    public void testGetAllUsers() {
        // Create test users
        User user1 = new User("U001", "user1", "password", "User 1", "<EMAIL>", "Address 1", "123-456-7890",
                true);
        User user2 = new User("U002", "user2", "password", "User 2", "<EMAIL>", "Address 2", "987-654-3210",
                false);

        List<User> users = Arrays.asList(user1, user2);

        // Mock the repository behavior
        when(userRepository.findAll()).thenReturn(users);

        // Call the service method
        List<User> allUsers = userService.getAllUsers();

        // Verify the result
        assertNotNull(allUsers);
        assertEquals(2, allUsers.size());
        assertEquals(users, allUsers);

        // Verify the repository was called
        verify(userRepository).findAll();
    }

    /**
     * Tests updating a user.
     */
    @Test
    public void testUpdateUser() {
        // Create a test user
        User user = new User(
                "U001",
                "testuser",
                "password",
                "Test User",
                "<EMAIL>",
                "123 Test St",
                "123-456-7890",
                true);

        // Update the user
        user.setFullName("Updated User");
        user.setEmail("<EMAIL>");

        // Mock the repository behavior
        when(userRepository.update(user)).thenReturn(user);

        // Call the service method
        User updatedUser = userService.updateUser(user);

        // Verify the result
        assertNotNull(updatedUser);
        assertEquals("Updated User", updatedUser.getFullName());
        assertEquals("<EMAIL>", updatedUser.getEmail());

        // Verify the repository was called
        verify(userRepository).update(user);
    }

    /**
     * Tests deleting a user.
     */
    @Test
    public void testDeleteUser() {
        // Mock the repository behavior
        when(userRepository.delete("U001")).thenReturn(true);
        when(userRepository.delete("U999")).thenReturn(false);

        // Call the service method
        boolean deleted1 = userService.deleteUser("U001");
        boolean deleted2 = userService.deleteUser("U999");

        // Verify the results
        assertTrue(deleted1);
        assertFalse(deleted2);

        // Verify the repository was called
        verify(userRepository).delete("U001");
        verify(userRepository).delete("U999");
    }
}
