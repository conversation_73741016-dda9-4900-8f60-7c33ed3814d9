package com.syos.test.service;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import static org.junit.Assert.*;

import com.syos.service.UserService;
import com.syos.model.User;
import com.syos.repository.UserRepository;
import com.syos.repository.impl.FileUserRepository;
import java.util.List;
import java.util.Optional;

/**
 * Comprehensive unit tests for UserService
 * Tests all user management operations and edge cases
 */
public class UserServiceTest {

    private UserService userService;
    private UserRepository userRepository;

    @Before
    public void setUp() {
        // Use file-based repository for testing
        userRepository = new FileUserRepository();
        userService = new UserService(userRepository);

        // Clean up any existing test data
        cleanupTestData();
    }

    @After
    public void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        // Remove any test users that might exist
        String[] testUserIds = { "TEST_U001", "TEST_U002", "TEST_U003", "TEST_U004", "TEST_U005" };
        for (String userId : testUserIds) {
            userRepository.delete(userId);
        }
    }

    // ========== USER REGISTRATION TESTS ==========

    @Test
    public void testRegisterValidUser() {
        User user = new User("TEST_U001", "testuser", "password123", "Test User",
                "<EMAIL>", "123 Test St", "1234567890", true);

        User registeredUser = userService.registerUser(user);

        assertNotNull("Registered user should not be null", registeredUser);
        assertEquals("User ID should match", "TEST_U001", registeredUser.getUserId());
        assertEquals("Username should match", "testuser", registeredUser.getUsername());
        assertEquals("Full name should match", "Test User", registeredUser.getFullName());
        assertTrue("Should be online customer", registeredUser.isOnlineCustomer());
    }

    @Test
    public void testRegisterUserWithDuplicateId() {
        User user1 = new User("TEST_U002", "user1", "password123", "User One",
                "<EMAIL>", "123 Test St", "1234567890", true);
        User user2 = new User("TEST_U002", "user2", "password456", "User Two",
                "<EMAIL>", "456 Test Ave", "0987654321", false);

        userService.registerUser(user1);
        User result = userService.registerUser(user2);

        // Should update existing user
        assertNotNull("Result should not be null", result);
        assertEquals("Username should be updated", "user2", result.getUsername());
        assertEquals("Full name should be updated", "User Two", result.getFullName());
    }

    // ========== USER AUTHENTICATION TESTS ==========

    @Test
    public void testAuthenticateWithValidCredentials() {
        User user = new User("TEST_U005", "logintest", "password123", "Login Test",
                "<EMAIL>", "123 Test St", "1234567890", true);
        userService.registerUser(user);

        Optional<User> authenticatedUser = userService.authenticateUser("logintest", "password123");

        assertTrue("Authentication should succeed", authenticatedUser.isPresent());
        assertEquals("Username should match", "logintest", authenticatedUser.get().getUsername());
        assertEquals("User ID should match", "TEST_U005", authenticatedUser.get().getUserId());
    }

    @Test
    public void testAuthenticateWithInvalidUsername() {
        Optional<User> result = userService.authenticateUser("nonexistent", "password123");
        assertFalse("Authentication should fail for non-existent user", result.isPresent());
    }

    @Test
    public void testAuthenticateWithInvalidPassword() {
        User user = new User("TEST_U001", "testuser", "password123", "Test User",
                "<EMAIL>", "123 Test St", "1234567890", true);
        userService.registerUser(user);

        Optional<User> result = userService.authenticateUser("testuser", "wrongpassword");
        assertFalse("Authentication should fail for wrong password", result.isPresent());
    }

    @Test
    public void testAuthenticateWithNullCredentials() {
        Optional<User> result1 = userService.authenticateUser(null, "password123");
        Optional<User> result2 = userService.authenticateUser("testuser", null);
        Optional<User> result3 = userService.authenticateUser(null, null);

        assertFalse("Authentication should fail for null username", result1.isPresent());
        assertFalse("Authentication should fail for null password", result2.isPresent());
        assertFalse("Authentication should fail for null credentials", result3.isPresent());
    }

    @Test
    public void testAuthenticateWithEmptyCredentials() {
        Optional<User> result1 = userService.authenticateUser("", "password123");
        Optional<User> result2 = userService.authenticateUser("testuser", "");
        Optional<User> result3 = userService.authenticateUser("", "");

        assertFalse("Authentication should fail for empty username", result1.isPresent());
        assertFalse("Authentication should fail for empty password", result2.isPresent());
        assertFalse("Authentication should fail for empty credentials", result3.isPresent());
    }

    // ========== USER RETRIEVAL TESTS ==========

    @Test
    public void testGetUserById() {
        User user = new User("TEST_U001", "findtest", "password123", "Find Test",
                "<EMAIL>", "123 Test St", "1234567890", false);
        userService.registerUser(user);

        Optional<User> foundUser = userService.getUserById("TEST_U001");

        assertTrue("User should be found", foundUser.isPresent());
        assertEquals("User ID should match", "TEST_U001", foundUser.get().getUserId());
        assertEquals("Username should match", "findtest", foundUser.get().getUsername());
    }

    @Test
    public void testGetUserByNonExistentId() {
        Optional<User> result = userService.getUserById("NONEXISTENT");
        assertFalse("Should not find non-existent user", result.isPresent());
    }

    @Test
    public void testGetUserByUsername() {
        User user = new User("TEST_U002", "usernametest", "password123", "Username Test",
                "<EMAIL>", "123 Test St", "1234567890", true);
        userService.registerUser(user);

        Optional<User> foundUser = userService.getUserByUsername("usernametest");

        assertTrue("User should be found", foundUser.isPresent());
        assertEquals("Username should match", "usernametest", foundUser.get().getUsername());
        assertEquals("User ID should match", "TEST_U002", foundUser.get().getUserId());
    }

    @Test
    public void testGetAllUsers() {
        User user1 = new User("TEST_U001", "user1", "password123", "User One",
                "<EMAIL>", "123 Test St", "1234567890", true);
        User user2 = new User("TEST_U002", "user2", "password456", "User Two",
                "<EMAIL>", "456 Test Ave", "0987654321", false);

        userService.registerUser(user1);
        userService.registerUser(user2);

        List<User> allUsers = userService.getAllUsers();

        assertNotNull("Users list should not be null", allUsers);
        assertTrue("Should have at least 2 users", allUsers.size() >= 2);

        boolean foundUser1 = allUsers.stream().anyMatch(u -> "TEST_U001".equals(u.getUserId()));
        boolean foundUser2 = allUsers.stream().anyMatch(u -> "TEST_U002".equals(u.getUserId()));

        assertTrue("Should contain user1", foundUser1);
        assertTrue("Should contain user2", foundUser2);
    }

    // ========== USER UPDATE TESTS ==========

    @Test
    public void testUpdateUser() {
        User user = new User("TEST_U001", "updatetest", "password123", "Update Test",
                "<EMAIL>", "123 Test St", "1234567890", true);
        userService.registerUser(user);

        // Update user details
        user.setFullName("Updated Name");
        user.setEmail("<EMAIL>");
        user.setOnlineCustomer(false);

        User updatedUser = userService.updateUser(user);

        assertNotNull("Updated user should not be null", updatedUser);
        assertEquals("Full name should be updated", "Updated Name", updatedUser.getFullName());
        assertEquals("Email should be updated", "<EMAIL>", updatedUser.getEmail());
        assertFalse("Online customer status should be updated", updatedUser.isOnlineCustomer());
    }

    // ========== USER DELETION TESTS ==========

    @Test
    public void testDeleteUser() {
        User user = new User("TEST_U001", "deletetest", "password123", "Delete Test",
                "<EMAIL>", "123 Test St", "1234567890", true);
        userService.registerUser(user);

        boolean deleted = userService.deleteUser("TEST_U001");
        assertTrue("User should be deleted successfully", deleted);

        Optional<User> foundUser = userService.getUserById("TEST_U001");
        assertFalse("Deleted user should not be found", foundUser.isPresent());
    }

    @Test
    public void testDeleteNonExistentUser() {
        boolean deleted = userService.deleteUser("NONEXISTENT");
        assertFalse("Should not delete non-existent user", deleted);
    }

    // ========== EDGE CASE TESTS ==========

    @Test(expected = IllegalArgumentException.class)
    public void testUserServiceWithNullRepository() {
        new UserService(null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRegisterNullUser() {
        userService.registerUser(null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRegisterUserWithDuplicateUsername() {
        User user1 = new User("TEST_U001", "duplicate", "password123", "User One",
                "<EMAIL>", "123 Test St", "1234567890", true);
        User user2 = new User("TEST_U002", "duplicate", "password456", "User Two",
                "<EMAIL>", "456 Test Ave", "0987654321", false);

        userService.registerUser(user1);
        userService.registerUser(user2); // Should throw exception
    }
}
