# Complete MySQL Setup Guide for SYOS

This guide will walk you through setting up MySQL and configuring the SYOS application to use it.

## Prerequisites

- Windows 10/11
- Java 8 or higher
- Administrative privileges

## Step 1: Install MySQL Server

### Option A: Download MySQL Installer (Recommended)

1. **Download MySQL Installer**
   - Go to: https://dev.mysql.com/downloads/installer/
   - Download "MySQL Installer for Windows" (mysql-installer-web-community-8.0.xx.x.msi)

2. **Run the Installer**
   - Right-click the downloaded file and "Run as administrator"
   - Choose "Developer Default" setup type
   - Click "Next" through the installation

3. **Configure MySQL Server**
   - **Config Type**: Development Computer
   - **Connectivity**: 
     - Port: 3306 (default)
     - X Protocol Port: 33060 (default)
   - **Authentication Method**: Use Strong Password Encryption
   - **Accounts and Roles**:
     - Set root password: `root` (for simplicity)
     - Create user account (optional): `syos_user` with password `syos_password`

4. **Complete Installation**
   - Apply configuration
   - Start MySQL Server
   - Finish installation

### Option B: Using MySQL ZIP Archive

1. Download MySQL Community Server ZIP from MySQL website
2. Extract to `C:\mysql`
3. Add `C:\mysql\bin` to your PATH environment variable
4. Initialize MySQL: `mysqld --initialize-insecure`
5. Start MySQL service: `mysqld --console`

## Step 2: Verify MySQL Installation

1. **Open Command Prompt as Administrator**
2. **Test MySQL Connection**:
   ```cmd
   mysql -u root -p
   ```
   - Enter password: `root`
   - You should see MySQL prompt: `mysql>`

3. **Exit MySQL**:
   ```sql
   exit;
   ```

## Step 3: Create Database and User

1. **Connect to MySQL**:
   ```cmd
   mysql -u root -p
   ```

2. **Create Database**:
   ```sql
   CREATE DATABASE syos_db;
   ```

3. **Create User (Optional)**:
   ```sql
   CREATE USER 'syos_user'@'localhost' IDENTIFIED BY 'syos_password';
   GRANT ALL PRIVILEGES ON syos_db.* TO 'syos_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **Verify Database**:
   ```sql
   SHOW DATABASES;
   USE syos_db;
   ```

5. **Exit MySQL**:
   ```sql
   exit;
   ```

## Step 4: Configure SYOS Application

### Create Environment Configuration

1. **Create .env file** in the project root directory
2. **Add database configuration** (see .env file created separately)

### Update Database Configuration

The application will automatically use the .env file settings or fall back to defaults.

## Step 5: Initialize Database Schema

1. **Run Database Setup Script**:
   ```cmd
   setup-mysql.bat
   ```

   Or manually:
   ```cmd
   mysql -u root -p syos_db < database\setup.sql
   mysql -u root -p syos_db < database\sample_data.sql
   ```

## Step 6: Run the Application

1. **Start SYOS with MySQL**:
   ```cmd
   run-mysql.bat
   ```

   Or use the regular run command:
   ```cmd
   run.bat
   ```

## Troubleshooting

### Common Issues

1. **"Access denied for user 'root'@'localhost'"**
   - Check password in .env file
   - Verify MySQL user exists
   - Reset root password if needed

2. **"Can't connect to MySQL server"**
   - Ensure MySQL service is running
   - Check port 3306 is not blocked
   - Verify MySQL is installed correctly

3. **"Unknown database 'syos_db'"**
   - Create database manually: `CREATE DATABASE syos_db;`
   - Run setup scripts

### Reset MySQL Root Password

1. **Stop MySQL Service**:
   ```cmd
   net stop mysql80
   ```

2. **Start MySQL in Safe Mode**:
   ```cmd
   mysqld --skip-grant-tables
   ```

3. **Connect and Reset Password**:
   ```cmd
   mysql -u root
   ```
   ```sql
   USE mysql;
   UPDATE user SET authentication_string=PASSWORD('root') WHERE User='root';
   FLUSH PRIVILEGES;
   exit;
   ```

4. **Restart MySQL Service**:
   ```cmd
   net start mysql80
   ```

## Verification Steps

1. **Check MySQL Service**:
   ```cmd
   net start | findstr MySQL
   ```

2. **Test Database Connection**:
   ```cmd
   mysql -u root -p -e "SHOW DATABASES;"
   ```

3. **Verify SYOS Tables**:
   ```cmd
   mysql -u root -p syos_db -e "SHOW TABLES;"
   ```

4. **Run SYOS Application**:
   ```cmd
   run-mysql.bat
   ```

## Next Steps

After successful setup:
1. Test all SYOS features
2. Create sample data
3. Generate reports
4. Backup database regularly

For additional help, refer to:
- MySQL Documentation: https://dev.mysql.com/doc/
- SYOS README.md file
- Project documentation files
