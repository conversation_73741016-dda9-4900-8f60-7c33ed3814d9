#!/bin/bash

echo "Starting SYOS with MySQL Database..."

# Set MySQL database configuration (you can modify these)
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=syos_db
export DB_USERNAME=root
export DB_PASSWORD=

# Set storage type to MySQL
export STORAGE_TYPE=mysql

# Create necessary directories
mkdir -p bin reports

echo "Compiling application..."
javac -d bin -cp "lib/mysql-connector-java-8.0.33.jar" com/syos/config/*.java com/syos/util/*.java com/syos/model/*.java com/syos/repository/*.java com/syos/repository/impl/*.java com/syos/factory/*.java com/syos/service/*.java com/syos/ui/*.java com/syos/*.java

if [ $? -ne 0 ]; then
    echo "Compilation failed!"
    exit 1
fi

echo "Running SYOS with MySQL database..."
java -cp "bin:lib/mysql-connector-java-8.0.33.jar" \
     -Dstorage.type=mysql \
     -Ddb.host=$DB_HOST \
     -Ddb.port=$DB_PORT \
     -Ddb.name=$DB_NAME \
     -Ddb.username=$DB_USERNAME \
     -Ddb.password=$DB_PASSWORD \
     com.syos.SyosApplication
