package com.syos.factory;

import com.syos.repository.*;

/**
 * Factory interface for creating repository instances.
 * This allows switching between different storage implementations (File, MySQL, etc.).
 */
public interface RepositoryFactory {
    
    /**
     * Gets an ItemRepository instance.
     * 
     * @return The ItemRepository
     */
    ItemRepository getItemRepository();
    
    /**
     * Gets a StockRepository instance.
     * 
     * @return The StockRepository
     */
    StockRepository getStockRepository();
    
    /**
     * Gets a UserRepository instance.
     * 
     * @return The UserRepository
     */
    UserRepository getUserRepository();
    
    /**
     * Gets a TransactionRepository instance.
     * 
     * @return The TransactionRepository
     */
    TransactionRepository getTransactionRepository();
    
    /**
     * Gets a BillRepository instance.
     * 
     * @return The BillRepository
     */
    BillRepository getBillRepository();
}
