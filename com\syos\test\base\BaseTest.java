package com.syos.test.base;

import com.syos.test.util.TestDataUtil;
import com.syos.factory.*;
import com.syos.util.DatabaseUtil;
import org.junit.After;
import org.junit.Before;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

/**
 * Base class for all tests.
 * Provides common setup and teardown methods.
 */
public abstract class BaseTest {

    protected final ByteArrayOutputStream outContent = new ByteArrayOutputStream();
    protected final ByteArrayOutputStream errContent = new ByteArrayOutputStream();
    protected final PrintStream originalOut = System.out;
    protected final PrintStream originalErr = System.err;

    /**
     * Sets up the test environment.
     * Creates the test data directory and redirects System.out and System.err.
     */
    @Before
    public void setUp() {
        // Determine test storage type (default: file for tests)
        String testStorageType = System.getProperty("test.storage.type", "file");

        if ("mysql".equalsIgnoreCase(testStorageType)) {
            // Set test database properties
            System.setProperty("db.name", "syos_test_db");
            try {
                DatabaseUtil.initializeDatabase();
            } catch (Exception e) {
                System.err
                        .println("Failed to initialize test database, falling back to file storage: " + e.getMessage());
                testStorageType = "file";
            }
        }

        if ("file".equalsIgnoreCase(testStorageType)) {
            // Set the data directory for testing
            System.setProperty("data.directory", TestDataUtil.TEST_DATA_DIR);
            // Set the reports directory for testing
            System.setProperty("reports.directory", TestDataUtil.TEST_DATA_DIR);
            TestDataUtil.setupTestDataDirectory();
        }

        System.setOut(new PrintStream(outContent));
        System.setErr(new PrintStream(errContent));
    }

    /**
     * Tears down the test environment.
     * Cleans up the test data directory and restores System.out and System.err.
     */
    @After
    public void tearDown() {
        String testStorageType = System.getProperty("test.storage.type", "file");

        if ("file".equalsIgnoreCase(testStorageType)) {
            TestDataUtil.cleanupTestDataDirectory();
        } else if ("mysql".equalsIgnoreCase(testStorageType)) {
            // Clean up test database if needed
            DatabaseUtil.closeConnection();
        }

        System.setOut(originalOut);
        System.setErr(originalErr);
        // Clear the system properties
        System.clearProperty("data.directory");
        System.clearProperty("reports.directory");
        System.clearProperty("db.name");
    }
}
