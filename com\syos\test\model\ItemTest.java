package com.syos.test.model;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.model.Item;

/**
 * Unit tests for Item model class
 */
public class ItemTest {
    
    private Item item;
    
    @Before
    public void setUp() {
        item = new Item("I001", "Rice", 2.50, "Groceries");
    }
    
    @Test
    public void testItemCreation() {
        assertNotNull("Item should not be null", item);
        assertEquals("Item code should match", "I001", item.getCode());
        assertEquals("Item name should match", "Rice", item.getName());
        assertEquals("Item price should match", 2.50, item.getPrice(), 0.01);
        assertEquals("Item category should match", "Groceries", item.getCategory());
    }
    
    @Test
    public void testItemCodeValidation() {
        // Test valid codes
        assertTrue("Valid code should be accepted", Item.isValidCode("I001"));
        assertTrue("Valid code should be accepted", Item.isValidCode("I999"));
        
        // Test invalid codes
        assertFalse("Null code should be invalid", Item.isValidCode(null));
        assertFalse("Empty code should be invalid", Item.isValidCode(""));
        assertFalse("Short code should be invalid", Item.isValidCode("I1"));
    }
    
    @Test
    public void testItemPriceValidation() {
        // Test valid prices
        assertTrue("Positive price should be valid", Item.isValidPrice(1.0));
        assertTrue("Zero price should be valid", Item.isValidPrice(0.0));
        
        // Test invalid prices
        assertFalse("Negative price should be invalid", Item.isValidPrice(-1.0));
    }
    
    @Test
    public void testItemEquality() {
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item3 = new Item("I002", "Wheat", 3.00, "Groceries");
        
        assertEquals("Items with same code should be equal", item1, item2);
        assertNotEquals("Items with different codes should not be equal", item1, item3);
    }
    
    @Test
    public void testItemToString() {
        String expected = "Item{code='I001', name='Rice', price=2.5, category='Groceries'}";
        assertEquals("toString should match expected format", expected, item.toString());
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidItemCreation() {
        new Item(null, "Test", 1.0, "Category");
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testNegativePriceCreation() {
        new Item("I001", "Test", -1.0, "Category");
    }
}
