# SYOS - Synex Outlet Store Management System

A comprehensive Java-based grocery store billing and stock management system implementing clean code principles, SOLID design patterns, and MySQL database integration.

## 🚀 Quick Start

### Prerequisites
- Java 8 or higher
- MySQL 8.0 or higher
- Windows OS (for .bat scripts)

### Setup & Run
1. **Configure Database**: Edit `.env` file with your MySQL credentials
2. **Setup Database**: Run the SQL scripts in `database/` folder
3. **Run Application**: Execute `run.bat`

```bash
# Quick start
run.bat
```

## 📁 Project Structure

```
SYOS/
├── com/syos/           # Source code
│   ├── config/         # Configuration classes
│   ├── factory/        # Factory pattern implementations
│   ├── model/          # Data models
│   ├── repository/     # Data access layer
│   ├── service/        # Business logic layer
│   ├── ui/             # User interface layer
│   └── util/           # Utility classes
├── database/           # SQL setup scripts
├── lib/                # Dependencies (MySQL connector)
├── reports/            # Generated reports
├── .env                # Database configuration
└── run.bat             # Application launcher
```

## 🎯 Features

### Core Functionality
- **Billing Operations**: In-store and online transactions
- **Inventory Management**: Item and stock management
- **Reports**: Sales, stock, and operational reports
- **User Management**: User authentication and management

### Technical Features
- **Clean Architecture**: Layered architecture with clear separation
- **SOLID Principles**: Single Responsibility, Open/Closed, etc.
- **Design Patterns**: Factory, Repository, Service Layer patterns
- **MySQL Integration**: Full database persistence
- **Error Handling**: Graceful error handling and recovery

## 🛠️ Configuration

### Database Setup (.env)
```properties
DB_HOST=localhost
DB_PORT=3306
DB_NAME=syos
DB_USERNAME=root
DB_PASSWORD=your_password
```

### Database Initialization
1. Create database: `CREATE DATABASE syos;`
2. Run setup script: `database/setup.sql`
3. Load sample data: `database/sample_data.sql`

## 📊 Usage

### Main Menu Options
1. **Billing Operations** - Process transactions
2. **Inventory Management** - Manage items and stock
3. **Reports** - Generate various reports
4. **User Management** - Manage system users
5. **Exit** - Close application

### Sample Workflow
1. Add items to inventory
2. Add stock batches
3. Process customer transactions
4. Generate reports

## 🧪 Testing

The system includes comprehensive error handling and graceful degradation when database is unavailable.

## 📝 Assignment Requirements

✅ **Object-Oriented Programming**: Classes, inheritance, polymorphism
✅ **Clean Code Principles**: Readable, maintainable code
✅ **SOLID Principles**: All five principles implemented
✅ **Design Patterns**: Factory, Repository, Service Layer patterns
✅ **Database Integration**: MySQL with proper connection handling
✅ **Console Interface**: User-friendly menu system
✅ **Error Handling**: Robust error management

## 🏗️ Architecture

### Layers
- **Presentation Layer**: UI classes for user interaction
- **Service Layer**: Business logic and operations
- **Repository Layer**: Data access abstraction
- **Model Layer**: Domain entities and data structures

### Design Patterns Used
- **Factory Pattern**: Repository and service creation
- **Repository Pattern**: Data access abstraction
- **Service Layer Pattern**: Business logic encapsulation

## 🔧 Development

### Building
```bash
javac -cp "lib/*" -d . com/syos/*.java com/syos/*/*.java com/syos/*/*/*.java
```

### Running
```bash
java -cp "lib/*;." com.syos.SyosApplication
```

## 📄 License

This project is developed for educational purposes as part of Clean Coding and Concurrent Programming assignment.

---

**Developed by**: [Your Name]
**Course**: Clean Coding and Concurrent Programming
**Institution**: [Your Institution]
