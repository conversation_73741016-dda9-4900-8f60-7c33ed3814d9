package com.syos.repository.impl;

import com.syos.model.*;
import com.syos.repository.TransactionRepository;
import com.syos.util.DatabaseUtil;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MySQL implementation of TransactionRepository.
 */
public class MySqlTransactionRepository implements TransactionRepository {

    @Override
    public Transaction save(Transaction transaction) {
        Connection conn = null;
        try {
            conn = DatabaseUtil.getConnection();
            conn.setAutoCommit(false); // Start transaction

            // Save transaction
            String transactionSql = """
                        INSERT INTO transactions (transaction_id, user_id, transaction_type, total_amount,
                        discount_amount, final_amount, cash_tendered, change_amount, transaction_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                        total_amount = VALUES(total_amount),
                        discount_amount = VALUES(discount_amount),
                        final_amount = VALUES(final_amount),
                        cash_tendered = VALUES(cash_tendered),
                        change_amount = VALUES(change_amount)
                    """;

            try (PreparedStatement stmt = conn.prepareStatement(transactionSql)) {
                stmt.setString(1, transaction.getTransactionId());
                stmt.setString(2, transaction.getUser() != null ? transaction.getUser().getUserId() : null);
                stmt.setString(3, transaction.getType().toString());
                stmt.setDouble(4, transaction.getTotalAmount());
                stmt.setDouble(5, transaction.getDiscountAmount());
                stmt.setDouble(6, transaction.getFinalAmount());
                stmt.setDouble(7, transaction.getCashTendered());
                stmt.setDouble(8, transaction.getChange());
                stmt.setTimestamp(9, Timestamp.valueOf(transaction.getDateTime()));

                stmt.executeUpdate();
            }

            // Delete existing transaction items
            String deleteItemsSql = "DELETE FROM transaction_items WHERE transaction_id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(deleteItemsSql)) {
                stmt.setString(1, transaction.getTransactionId());
                stmt.executeUpdate();
            }

            // Save transaction items
            String itemsSql = """
                        INSERT INTO transaction_items (transaction_id, item_code, quantity, unit_price, subtotal)
                        VALUES (?, ?, ?, ?, ?)
                    """;

            try (PreparedStatement stmt = conn.prepareStatement(itemsSql)) {
                for (CartItem cartItem : transaction.getItems()) {
                    stmt.setString(1, transaction.getTransactionId());
                    stmt.setString(2, cartItem.getItem().getCode());
                    stmt.setInt(3, cartItem.getQuantity());
                    stmt.setDouble(4, cartItem.getItem().getPrice());
                    stmt.setDouble(5, cartItem.getSubtotal());
                    stmt.addBatch();
                }
                stmt.executeBatch();
            }

            conn.commit();
            return transaction;

        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    System.err.println("Error rolling back transaction: " + ex.getMessage());
                }
            }
            System.err.println("Error saving transaction: " + e.getMessage());
            e.printStackTrace();
            return null;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                    System.err.println("Error resetting auto-commit: " + e.getMessage());
                }
            }
        }
    }

    @Override
    public Optional<Transaction> findById(String transactionId) {
        String sql = """
                    SELECT t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM transactions t
                    LEFT JOIN users u ON t.user_id = u.user_id
                    WHERE t.transaction_id = ?
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, transactionId);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                Transaction transaction = createTransactionFromResultSet(rs);
                loadTransactionItems(transaction);
                return Optional.of(transaction);
            }

        } catch (SQLException e) {
            System.err.println("Error finding transaction by ID: " + e.getMessage());
            e.printStackTrace();
        }

        return Optional.empty();
    }

    @Override
    public List<Transaction> findByUserId(String userId) {
        return findByUser(userId);
    }

    @Override
    public List<Transaction> findByType(TransactionType type) {
        List<Transaction> transactions = new ArrayList<>();
        String sql = """
                    SELECT t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM transactions t
                    LEFT JOIN users u ON t.user_id = u.user_id
                    WHERE t.transaction_type = ?
                    ORDER BY t.transaction_date DESC
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, type.toString());
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Transaction transaction = createTransactionFromResultSet(rs);
                loadTransactionItems(transaction);
                transactions.add(transaction);
            }

        } catch (SQLException e) {
            System.err.println("Error finding transactions by type: " + e.getMessage());
            e.printStackTrace();
        }

        return transactions;
    }

    @Override
    public String getNextTransactionId() {
        String sql = "SELECT MAX(CAST(SUBSTRING(transaction_id, 2) AS UNSIGNED)) FROM transactions WHERE transaction_id LIKE 'T%'";

        try (Connection conn = DatabaseUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                int maxNumber = rs.getInt(1);
                return String.format("T%03d", maxNumber + 1);
            }

        } catch (SQLException e) {
            System.err.println("Error getting next transaction ID: " + e.getMessage());
            e.printStackTrace();
        }

        return "T001"; // Default first transaction ID
    }

    @Override
    public List<Transaction> findByDate(LocalDate date) {
        List<Transaction> transactions = new ArrayList<>();
        String sql = """
                    SELECT t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM transactions t
                    LEFT JOIN users u ON t.user_id = u.user_id
                    WHERE DATE(t.transaction_date) = ?
                    ORDER BY t.transaction_date DESC
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setDate(1, Date.valueOf(date));
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Transaction transaction = createTransactionFromResultSet(rs);
                loadTransactionItems(transaction);
                transactions.add(transaction);
            }

        } catch (SQLException e) {
            System.err.println("Error finding transactions by date: " + e.getMessage());
            e.printStackTrace();
        }

        return transactions;
    }

    public List<Transaction> findByUser(String userId) {
        List<Transaction> transactions = new ArrayList<>();
        String sql = """
                    SELECT t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM transactions t
                    LEFT JOIN users u ON t.user_id = u.user_id
                    WHERE t.user_id = ?
                    ORDER BY t.transaction_date DESC
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, userId);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Transaction transaction = createTransactionFromResultSet(rs);
                loadTransactionItems(transaction);
                transactions.add(transaction);
            }

        } catch (SQLException e) {
            System.err.println("Error finding transactions by user: " + e.getMessage());
            e.printStackTrace();
        }

        return transactions;
    }

    @Override
    public List<Transaction> findAll() {
        List<Transaction> transactions = new ArrayList<>();
        String sql = """
                    SELECT t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM transactions t
                    LEFT JOIN users u ON t.user_id = u.user_id
                    ORDER BY t.transaction_date DESC
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                Transaction transaction = createTransactionFromResultSet(rs);
                loadTransactionItems(transaction);
                transactions.add(transaction);
            }

        } catch (SQLException e) {
            System.err.println("Error finding all transactions: " + e.getMessage());
            e.printStackTrace();
        }

        return transactions;
    }

    @Override
    public boolean delete(String transactionId) {
        String sql = "DELETE FROM transactions WHERE transaction_id = ?";

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, transactionId);
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;

        } catch (SQLException e) {
            System.err.println("Error deleting transaction: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Creates a Transaction object from a ResultSet.
     */
    private Transaction createTransactionFromResultSet(ResultSet rs) throws SQLException {
        User user = null;
        if (rs.getString("user_id") != null) {
            user = new User(
                    rs.getString("user_id"),
                    rs.getString("username"),
                    rs.getString("password"),
                    rs.getString("full_name"),
                    rs.getString("email"),
                    rs.getString("address"),
                    rs.getString("phone"),
                    rs.getBoolean("is_online_customer"));
        }

        Transaction transaction = new Transaction(
                rs.getString("transaction_id"),
                user,
                TransactionType.valueOf(rs.getString("transaction_type")));

        // Set the transaction date
        Timestamp timestamp = rs.getTimestamp("transaction_date");
        if (timestamp != null) {
            transaction.setDateTime(timestamp.toLocalDateTime());
        }

        // Set amounts
        transaction.setTotalAmount(rs.getDouble("total_amount"));
        transaction.setDiscountAmount(rs.getDouble("discount_amount"));
        transaction.setFinalAmount(rs.getDouble("final_amount"));
        transaction.setCashTendered(rs.getDouble("cash_tendered"));
        transaction.setChange(rs.getDouble("change_amount"));

        return transaction;
    }

    /**
     * Loads transaction items for a transaction.
     */
    private void loadTransactionItems(Transaction transaction) throws SQLException {
        String sql = """
                    SELECT ti.*, i.name, i.category
                    FROM transaction_items ti
                    JOIN items i ON ti.item_code = i.code
                    WHERE ti.transaction_id = ?
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, transaction.getTransactionId());
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Item item = new Item(
                        rs.getString("item_code"),
                        rs.getString("name"),
                        rs.getDouble("unit_price"),
                        rs.getString("category"));

                transaction.addItem(item, rs.getInt("quantity"));
            }
        }
    }
}
