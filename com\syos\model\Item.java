package com.syos.model;

/**
 * Represents an item in the store inventory.
 * Each item has a unique code, name, price, and category.
 */
public class Item {
    private String code;
    private String name;
    private double price;
    private String category;

    public Item() {
    }

    public Item(String code, String name, double price, String category) {
        if (!isValidCode(code)) {
            throw new IllegalArgumentException("Invalid item code: " + code);
        }
        if (!isValidPrice(price)) {
            throw new IllegalArgumentException("Invalid price: " + price);
        }
        this.code = code;
        this.name = name;
        this.price = price;
        this.category = category;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    @Override
    public String toString() {
        return "Item{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", price=" + price +
                ", category='" + category + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        Item item = (Item) o;
        return code.equals(item.code);
    }

    @Override
    public int hashCode() {
        return code.hashCode();
    }

    // Validation methods
    public static boolean isValidCode(String code) {
        return code != null && !code.trim().isEmpty() && code.length() >= 3;
    }

    public static boolean isValidPrice(double price) {
        return price >= 0.0;
    }
}
