package com.syos.test;

import com.syos.model.*;
import com.syos.service.*;
import com.syos.repository.impl.*;
import com.syos.util.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Simple test runner that demonstrates the SYOS system functionality
 * without requiring JUnit dependencies.
 * 
 * This runner tests the core functionality of all system components
 * and provides comprehensive validation of the system.
 */
public class SimpleTestRunner {
    
    private static int testCount = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("SYOS SYSTEM FUNCTIONALITY TEST RUNNER");
        System.out.println("=".repeat(80));
        System.out.println("Testing all core system components...\n");
        
        // Run all tests
        testModelClasses();
        testUtilityClasses();
        testServiceClasses();
        testRepositoryClasses();
        testIntegrationScenarios();
        
        // Print summary
        printTestSummary();
    }
    
    private static void testModelClasses() {
        System.out.println("📦 TESTING MODEL CLASSES");
        System.out.println("-".repeat(50));
        
        // Test Item class
        testItem();
        testUser();
        testStockBatch();
        testCartItem();
        testTransaction();
        testBill();
        
        System.out.println();
    }
    
    private static void testItem() {
        try {
            // Test Item creation
            Item item = new Item("I001", "Test Rice", 2.50, "Groceries");
            assert item.getCode().equals("I001") : "Item code should match";
            assert item.getName().equals("Test Rice") : "Item name should match";
            assert item.getPrice() == 2.50 : "Item price should match";
            assert item.getCategory().equals("Groceries") : "Item category should match";
            
            // Test validation methods
            assert Item.isValidCode("I001") : "Valid code should pass validation";
            assert !Item.isValidCode(null) : "Null code should fail validation";
            assert Item.isValidPrice(2.50) : "Valid price should pass validation";
            assert !Item.isValidPrice(-1.0) : "Negative price should fail validation";
            
            pass("Item class tests");
        } catch (Exception e) {
            fail("Item class tests", e);
        }
    }
    
    private static void testUser() {
        try {
            User user = new User("U001", "testuser", "password123", "Test User",
                "<EMAIL>", "123 Test St", "1234567890", true);
            
            assert user.getUserId().equals("U001") : "User ID should match";
            assert user.getUsername().equals("testuser") : "Username should match";
            assert user.validatePassword("password123") : "Password validation should work";
            assert !user.validatePassword("wrong") : "Wrong password should fail";
            assert User.isValidEmail("<EMAIL>") : "Valid email should pass";
            assert !User.isValidEmail("invalid") : "Invalid email should fail";
            
            pass("User class tests");
        } catch (Exception e) {
            fail("User class tests", e);
        }
    }
    
    private static void testStockBatch() {
        try {
            Item item = new Item("I001", "Test Item", 2.50, "Test");
            LocalDate received = LocalDate.now();
            LocalDate expiry = LocalDate.now().plusDays(30);
            
            StockBatch batch = new StockBatch("SB001", item, 100, received, expiry, StockType.SHELF);
            
            assert batch.getBatchId().equals("SB001") : "Batch ID should match";
            assert batch.getQuantity() == 100 : "Quantity should match";
            assert !batch.isExpired() : "Future expiry should not be expired";
            assert batch.getStockType() == StockType.SHELF : "Stock type should match";
            
            pass("StockBatch class tests");
        } catch (Exception e) {
            fail("StockBatch class tests", e);
        }
    }
    
    private static void testCartItem() {
        try {
            Item item = new Item("I001", "Test Item", 2.50, "Test");
            CartItem cartItem = new CartItem(item, 3);
            
            assert cartItem.getQuantity() == 3 : "Quantity should match";
            assert cartItem.getSubtotal() == 7.50 : "Subtotal should be calculated correctly";
            assert cartItem.getItem().equals(item) : "Item should match";
            
            // Test constructor with item code
            CartItem cartItem2 = new CartItem("I002", 2, 3.00);
            assert cartItem2.getQuantity() == 2 : "Quantity should match";
            assert cartItem2.getItemCode().equals("I002") : "Item code should match";
            assert cartItem2.getUnitPrice() == 3.00 : "Unit price should match";
            
            pass("CartItem class tests");
        } catch (Exception e) {
            fail("CartItem class tests", e);
        }
    }
    
    private static void testTransaction() {
        try {
            User user = new User("U001", "testuser", "password123", "Test User",
                "<EMAIL>", "123 Test St", "1234567890", true);
            
            Transaction transaction = new Transaction("T001", user, TransactionType.IN_STORE);
            transaction.addItem(new Item("I001", "Test Item", 2.50, "Test"), 2);
            transaction.calculateTotal();
            
            assert transaction.getTransactionId().equals("T001") : "Transaction ID should match";
            assert transaction.getType() == TransactionType.IN_STORE : "Type should match";
            assert transaction.getTotalAmount() == 5.00 : "Total should be calculated correctly";
            
            pass("Transaction class tests");
        } catch (Exception e) {
            fail("Transaction class tests", e);
        }
    }
    
    private static void testBill() {
        try {
            Transaction transaction = new Transaction();
            transaction.setTransactionId("T001");
            transaction.setTotalAmount(10.00);
            transaction.setFinalAmount(10.00);
            
            Bill bill = new Bill("B001", transaction);
            
            assert bill.getBillNumber().equals("B001") : "Bill number should match";
            assert bill.getTransaction().equals(transaction) : "Transaction should match";
            assert bill.getGeneratedDateTime() != null : "Generated date should be set";
            
            pass("Bill class tests");
        } catch (Exception e) {
            fail("Bill class tests", e);
        }
    }
    
    private static void testUtilityClasses() {
        System.out.println("🛠️ TESTING UTILITY CLASSES");
        System.out.println("-".repeat(50));
        
        testDateUtil();
        testFileUtil();
        
        System.out.println();
    }
    
    private static void testDateUtil() {
        try {
            LocalDate today = LocalDate.now();
            LocalDate future = today.plusDays(5);
            LocalDate past = today.minusDays(5);
            
            // Test formatting
            String formatted = DateUtil.formatDate(today);
            assert formatted != null && !formatted.isEmpty() : "Date formatting should work";
            
            // Test parsing
            LocalDate parsed = DateUtil.parseDate(formatted);
            assert parsed.equals(today) : "Date parsing should work";
            
            // Test comparisons
            assert DateUtil.isBefore(past, today) : "Past date should be before today";
            assert DateUtil.isAfter(future, today) : "Future date should be after today";
            
            // Test validation
            assert DateUtil.isValidDateFormat("2025-01-15") : "Valid date format should pass";
            assert !DateUtil.isValidDateFormat("invalid") : "Invalid date format should fail";
            
            pass("DateUtil class tests");
        } catch (Exception e) {
            fail("DateUtil class tests", e);
        }
    }
    
    private static void testFileUtil() {
        try {
            String testFile = "test_file.txt";
            List<String> testLines = List.of("Line 1", "Line 2", "Line 3");
            
            // Test writing
            boolean written = FileUtil.writeLinesToFile(testFile, testLines);
            assert written : "File writing should succeed";
            
            // Test reading
            List<String> readLines = FileUtil.readLinesFromFile(testFile);
            assert readLines.size() == 3 : "Should read 3 lines";
            assert readLines.get(0).equals("Line 1") : "First line should match";
            
            // Test file existence
            assert FileUtil.fileExists(testFile) : "File should exist";
            
            // Test deletion
            boolean deleted = FileUtil.deleteFile(testFile);
            assert deleted : "File deletion should succeed";
            assert !FileUtil.fileExists(testFile) : "File should not exist after deletion";
            
            pass("FileUtil class tests");
        } catch (Exception e) {
            fail("FileUtil class tests", e);
        }
    }
    
    private static void testServiceClasses() {
        System.out.println("🔧 TESTING SERVICE CLASSES");
        System.out.println("-".repeat(50));
        
        testUserService();
        testInventoryService();
        testReportService();
        
        System.out.println();
    }
    
    private static void testUserService() {
        try {
            UserService userService = new UserService(new FileUserRepository());
            
            User user = new User("TEST_U001", "testuser", "password123", "Test User",
                "<EMAIL>", "123 Test St", "1234567890", true);
            
            // Test registration
            User registered = userService.registerUser(user);
            assert registered != null : "User registration should succeed";
            assert registered.getUserId().equals("TEST_U001") : "User ID should match";
            
            // Test authentication
            Optional<User> authenticated = userService.authenticateUser("testuser", "password123");
            assert authenticated.isPresent() : "Authentication should succeed";
            assert authenticated.get().getUsername().equals("testuser") : "Username should match";
            
            // Test invalid authentication
            Optional<User> invalid = userService.authenticateUser("testuser", "wrong");
            assert !invalid.isPresent() : "Invalid authentication should fail";
            
            // Test user retrieval
            Optional<User> found = userService.getUserById("TEST_U001");
            assert found.isPresent() : "User should be found by ID";
            
            // Clean up
            userService.deleteUser("TEST_U001");
            
            pass("UserService class tests");
        } catch (Exception e) {
            fail("UserService class tests", e);
        }
    }
    
    private static void testInventoryService() {
        try {
            InventoryService inventoryService = new InventoryService(
                new FileItemRepository(), new FileStockRepository());
            
            // Test adding item
            Item item = new Item("TEST_I001", "Test Item", 2.50, "Test");
            Item added = inventoryService.addItem(item);
            assert added != null : "Item should be added";
            assert added.getCode().equals("TEST_I001") : "Item code should match";
            
            // Test adding stock
            StockBatch batch = new StockBatch("TEST_SB001", item, 100, 
                LocalDate.now(), LocalDate.now().plusDays(30), StockType.SHELF);
            StockBatch addedBatch = inventoryService.addStockBatch(batch);
            assert addedBatch != null : "Stock batch should be added";
            
            // Test getting total quantity
            int totalQuantity = inventoryService.getTotalQuantity("TEST_I001", StockType.SHELF);
            assert totalQuantity == 100 : "Total quantity should match";
            
            pass("InventoryService class tests");
        } catch (Exception e) {
            fail("InventoryService class tests", e);
        }
    }
    
    private static void testReportService() {
        try {
            ReportService reportService = new ReportService(
                new FileItemRepository(),
                new FileStockRepository(),
                new FileTransactionRepository(),
                new FileBillRepository(),
                new InventoryService(new FileItemRepository(), new FileStockRepository())
            );
            
            // Test report generation
            String stockReport = reportService.generateFullStockReport();
            assert stockReport != null && !stockReport.isEmpty() : "Stock report should be generated";
            assert FileUtil.fileExists(stockReport) : "Report file should exist";
            
            String reshelfReport = reportService.generateReshelfReport();
            assert reshelfReport != null && !reshelfReport.isEmpty() : "Reshelf report should be generated";
            
            pass("ReportService class tests");
        } catch (Exception e) {
            fail("ReportService class tests", e);
        }
    }
    
    private static void testRepositoryClasses() {
        System.out.println("💾 TESTING REPOSITORY CLASSES");
        System.out.println("-".repeat(50));
        
        testItemRepository();
        testUserRepository();
        testStockRepository();
        
        System.out.println();
    }
    
    private static void testItemRepository() {
        try {
            FileItemRepository repository = new FileItemRepository();
            
            Item item = new Item("REPO_TEST001", "Repository Test Item", 1.99, "Test");
            
            // Test save
            Item saved = repository.save(item);
            assert saved != null : "Item should be saved";
            assert saved.getCode().equals("REPO_TEST001") : "Item code should match";
            
            // Test find
            Optional<Item> found = repository.findByCode("REPO_TEST001");
            assert found.isPresent() : "Item should be found";
            assert found.get().getName().equals("Repository Test Item") : "Item name should match";
            
            // Test delete
            boolean deleted = repository.delete("REPO_TEST001");
            assert deleted : "Item should be deleted";
            
            pass("ItemRepository class tests");
        } catch (Exception e) {
            fail("ItemRepository class tests", e);
        }
    }
    
    private static void testUserRepository() {
        try {
            FileUserRepository repository = new FileUserRepository();
            
            User user = new User("REPO_U001", "repotest", "password123", "Repo Test",
                "<EMAIL>", "123 Test St", "1234567890", true);
            
            // Test save
            User saved = repository.save(user);
            assert saved != null : "User should be saved";
            
            // Test authentication
            Optional<User> authenticated = repository.authenticate("repotest", "password123");
            assert authenticated.isPresent() : "Authentication should succeed";
            
            // Test find by username
            Optional<User> found = repository.findByUsername("repotest");
            assert found.isPresent() : "User should be found by username";
            
            // Clean up
            repository.delete("REPO_U001");
            
            pass("UserRepository class tests");
        } catch (Exception e) {
            fail("UserRepository class tests", e);
        }
    }
    
    private static void testStockRepository() {
        try {
            FileStockRepository repository = new FileStockRepository();
            
            Item item = new Item("I001", "Test Item", 2.50, "Test");
            StockBatch batch = new StockBatch("REPO_SB001", item, 100,
                LocalDate.now(), LocalDate.now().plusDays(30), StockType.SHELF);
            
            // Test save
            StockBatch saved = repository.save(batch);
            assert saved != null : "Stock batch should be saved";
            
            // Test find
            Optional<StockBatch> found = repository.findByBatchId("REPO_SB001");
            assert found.isPresent() : "Stock batch should be found";
            
            // Clean up
            repository.delete("REPO_SB001");
            
            pass("StockRepository class tests");
        } catch (Exception e) {
            fail("StockRepository class tests", e);
        }
    }
    
    private static void testIntegrationScenarios() {
        System.out.println("🔄 TESTING INTEGRATION SCENARIOS");
        System.out.println("-".repeat(50));
        
        testCompleteInventoryWorkflow();
        testCompleteUserWorkflow();
        
        System.out.println();
    }
    
    private static void testCompleteInventoryWorkflow() {
        try {
            // Create services
            InventoryService inventoryService = new InventoryService(
                new FileItemRepository(), new FileStockRepository());
            
            // Add item
            Item item = new Item("INTEG_I001", "Integration Test Item", 3.99, "Test");
            Item addedItem = inventoryService.addItem(item);
            assert addedItem != null : "Item should be added in workflow";
            
            // Add stock
            StockBatch batch = new StockBatch("INTEG_SB001", addedItem, 50,
                LocalDate.now(), LocalDate.now().plusDays(30), StockType.SHELF);
            StockBatch addedBatch = inventoryService.addStockBatch(batch);
            assert addedBatch != null : "Stock should be added in workflow";
            
            // Check total quantity
            int total = inventoryService.getTotalQuantity("INTEG_I001", StockType.SHELF);
            assert total == 50 : "Total quantity should match in workflow";
            
            pass("Complete inventory workflow");
        } catch (Exception e) {
            fail("Complete inventory workflow", e);
        }
    }
    
    private static void testCompleteUserWorkflow() {
        try {
            UserService userService = new UserService(new FileUserRepository());
            
            // Register user
            User user = new User("INTEG_U001", "integuser", "password123", "Integration User",
                "<EMAIL>", "123 Test St", "1234567890", true);
            User registered = userService.registerUser(user);
            assert registered != null : "User should be registered in workflow";
            
            // Authenticate user
            Optional<User> authenticated = userService.authenticateUser("integuser", "password123");
            assert authenticated.isPresent() : "User should authenticate in workflow";
            
            // Update user
            user.setFullName("Updated Integration User");
            User updated = userService.updateUser(user);
            assert updated.getFullName().equals("Updated Integration User") : "User should be updated in workflow";
            
            // Clean up
            userService.deleteUser("INTEG_U001");
            
            pass("Complete user workflow");
        } catch (Exception e) {
            fail("Complete user workflow", e);
        }
    }
    
    private static void pass(String testName) {
        testCount++;
        passedTests++;
        System.out.println("✅ " + testName + " - PASSED");
    }
    
    private static void fail(String testName, Exception e) {
        testCount++;
        failedTests++;
        System.out.println("❌ " + testName + " - FAILED: " + e.getMessage());
    }
    
    private static void printTestSummary() {
        System.out.println("=".repeat(80));
        System.out.println("TEST EXECUTION SUMMARY");
        System.out.println("=".repeat(80));
        System.out.println("Total Tests: " + testCount);
        System.out.println("Passed: " + passedTests + " ✅");
        System.out.println("Failed: " + failedTests + " ❌");
        System.out.println("Success Rate: " + (passedTests * 100 / testCount) + "%");
        System.out.println("=".repeat(80));
        
        if (failedTests == 0) {
            System.out.println("🎉 ALL TESTS PASSED! The SYOS system is working correctly.");
        } else {
            System.out.println("⚠️  Some tests failed. Please check the error messages above.");
        }
        
        System.out.println("\n📋 SYSTEM COMPONENTS TESTED:");
        System.out.println("• Model Classes (Item, User, StockBatch, CartItem, Transaction, Bill)");
        System.out.println("• Utility Classes (DateUtil, FileUtil)");
        System.out.println("• Service Classes (UserService, InventoryService, ReportService)");
        System.out.println("• Repository Classes (ItemRepository, UserRepository, StockRepository)");
        System.out.println("• Integration Workflows (Complete user and inventory workflows)");
        System.out.println("\n🎯 This demonstrates comprehensive testing of the SYOS system!");
    }
}
