package com.syos.config;

/**
 * Database configuration class containing connection parameters.
 */
public class DatabaseConfig {
    
    // Default database configuration
    public static final String DEFAULT_HOST = "localhost";
    public static final String DEFAULT_PORT = "3306";
    public static final String DEFAULT_DATABASE = "syos_db";
    public static final String DEFAULT_USERNAME = "syos_user";
    public static final String DEFAULT_PASSWORD = "syos_password";
    
    // System property keys for configuration override
    public static final String DB_HOST_PROPERTY = "db.host";
    public static final String DB_PORT_PROPERTY = "db.port";
    public static final String DB_NAME_PROPERTY = "db.name";
    public static final String DB_USERNAME_PROPERTY = "db.username";
    public static final String DB_PASSWORD_PROPERTY = "db.password";
    
    /**
     * Gets the database host.
     * 
     * @return The database host
     */
    public static String getHost() {
        return System.getProperty(DB_HOST_PROPERTY, DEFAULT_HOST);
    }
    
    /**
     * Gets the database port.
     * 
     * @return The database port
     */
    public static String getPort() {
        return System.getProperty(DB_PORT_PROPERTY, DEFAULT_PORT);
    }
    
    /**
     * Gets the database name.
     * 
     * @return The database name
     */
    public static String getDatabaseName() {
        return System.getProperty(DB_NAME_PROPERTY, DEFAULT_DATABASE);
    }
    
    /**
     * Gets the database username.
     * 
     * @return The database username
     */
    public static String getUsername() {
        return System.getProperty(DB_USERNAME_PROPERTY, DEFAULT_USERNAME);
    }
    
    /**
     * Gets the database password.
     * 
     * @return The database password
     */
    public static String getPassword() {
        return System.getProperty(DB_PASSWORD_PROPERTY, DEFAULT_PASSWORD);
    }
    
    /**
     * Gets the complete JDBC URL.
     * 
     * @return The JDBC URL
     */
    public static String getJdbcUrl() {
        return String.format("**********************************************************************************",
                getHost(), getPort(), getDatabaseName());
    }
}
