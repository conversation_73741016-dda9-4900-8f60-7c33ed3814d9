package com.syos.config;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * Database configuration class containing connection parameters.
 * Supports loading configuration from .env file, system properties, or
 * defaults.
 */
public class DatabaseConfig {

    // Default database configuration
    public static final String DEFAULT_HOST = "localhost";
    public static final String DEFAULT_PORT = "3306";
    public static final String DEFAULT_DATABASE = "syos_db";
    public static final String DEFAULT_USERNAME = "root";
    public static final String DEFAULT_PASSWORD = "root";

    // System property keys for configuration override
    public static final String DB_HOST_PROPERTY = "db.host";
    public static final String DB_PORT_PROPERTY = "db.port";
    public static final String DB_NAME_PROPERTY = "db.name";
    public static final String DB_USERNAME_PROPERTY = "db.username";
    public static final String DB_PASSWORD_PROPERTY = "db.password";

    // Environment variable keys
    public static final String DB_HOST_ENV = "DB_HOST";
    public static final String DB_PORT_ENV = "DB_PORT";
    public static final String DB_NAME_ENV = "DB_NAME";
    public static final String DB_USERNAME_ENV = "DB_USERNAME";
    public static final String DB_PASSWORD_ENV = "DB_PASSWORD";

    private static Properties envProperties = null;

    static {
        loadEnvFile();
    }

    /**
     * Load configuration from .env file if it exists.
     */
    private static void loadEnvFile() {
        try {
            envProperties = new Properties();
            FileInputStream fis = new FileInputStream(".env");
            envProperties.load(fis);
            fis.close();
            System.out.println("Loaded database configuration from .env file");
        } catch (IOException e) {
            System.out.println("No .env file found, using system properties and defaults");
            envProperties = null;
        }
    }

    /**
     * Gets configuration value with priority: System Property > .env file > Default
     */
    private static String getConfigValue(String systemProperty, String envKey, String defaultValue) {
        // First check system properties
        String value = System.getProperty(systemProperty);
        if (value != null && !value.trim().isEmpty()) {
            return value;
        }

        // Then check .env file
        if (envProperties != null) {
            value = envProperties.getProperty(envKey);
            if (value != null && !value.trim().isEmpty()) {
                return value;
            }
        }

        // Finally use default
        return defaultValue;
    }

    /**
     * Gets the database host.
     *
     * @return The database host
     */
    public static String getHost() {
        return getConfigValue(DB_HOST_PROPERTY, DB_HOST_ENV, DEFAULT_HOST);
    }

    /**
     * Gets the database port.
     *
     * @return The database port
     */
    public static String getPort() {
        return getConfigValue(DB_PORT_PROPERTY, DB_PORT_ENV, DEFAULT_PORT);
    }

    /**
     * Gets the database name.
     *
     * @return The database name
     */
    public static String getDatabaseName() {
        return getConfigValue(DB_NAME_PROPERTY, DB_NAME_ENV, DEFAULT_DATABASE);
    }

    /**
     * Gets the database username.
     *
     * @return The database username
     */
    public static String getUsername() {
        return getConfigValue(DB_USERNAME_PROPERTY, DB_USERNAME_ENV, DEFAULT_USERNAME);
    }

    /**
     * Gets the database password.
     *
     * @return The database password
     */
    public static String getPassword() {
        return getConfigValue(DB_PASSWORD_PROPERTY, DB_PASSWORD_ENV, DEFAULT_PASSWORD);
    }

    /**
     * Gets the complete JDBC URL.
     * 
     * @return The JDBC URL
     */
    public static String getJdbcUrl() {
        return String.format("**********************************************************************************",
                getHost(), getPort(), getDatabaseName());
    }
}
