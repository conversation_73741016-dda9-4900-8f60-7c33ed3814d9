@echo off

echo ========================================
echo Simple SYOS Test Runner
echo ========================================

REM Create bin directory
if not exist bin mkdir bin

echo Compiling model classes...
javac -d bin com\syos\model\*.java

if %errorlevel% neq 0 (
    echo ERROR: Model compilation failed!
    pause
    exit /b 1
)

echo Model classes compiled successfully!

echo Compiling simple test...
javac -d bin -cp "bin;lib\junit-4.13.2.jar" com\syos\test\model\ItemTest.java

if %errorlevel% neq 0 (
    echo ERROR: Test compilation failed!
    pause
    exit /b 1
)

echo Test compiled successfully!

echo Running ItemTest...
java -cp "bin;lib\junit-4.13.2.jar" org.junit.runner.JUnitCore com.syos.test.model.ItemTest

echo ========================================
echo Test execution completed!
echo ========================================
pause
