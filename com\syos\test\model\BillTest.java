package com.syos.test.model;

import com.syos.model.*;
import com.syos.test.base.BaseTest;
import org.junit.Test;

import java.time.LocalDateTime;

import static org.junit.Assert.*;

/**
 * Unit tests for the Bill model class.
 */
public class BillTest extends BaseTest {

    /**
     * Tests the constructor and getters.
     */
    @Test
    public void testConstructorAndGetters() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);

        // Create a bill
        Bill bill = new Bill("B001", transaction);

        // Verify the values
        assertEquals("B001", bill.getBillNumber());
        assertEquals(transaction, bill.getTransaction());
        assertNotNull(bill.getGeneratedDateTime());
    }

    /**
     * Tests the setters.
     */
    @Test
    public void testSetters() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);

        // Create a bill
        Bill bill = new Bill();

        // Set values
        bill.setBillNumber("B001");
        bill.setTransaction(transaction);
        LocalDateTime dateTime = LocalDateTime.now().minusHours(1);
        bill.setGeneratedDateTime(dateTime);

        // Verify the values
        assertEquals("B001", bill.getBillNumber());
        assertEquals(transaction, bill.getTransaction());
        assertEquals(dateTime, bill.getGeneratedDateTime());
    }

    /**
     * Tests generating a formatted bill.
     */
    @Test
    public void testGenerateFormattedBill() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);

        // Create items
        Item item1 = new Item("I001", "Rice", 2.50, "Groceries");
        Item item2 = new Item("I002", "Flour", 1.80, "Groceries");

        // Add items
        transaction.addItem(item1, 3); // 3 * 2.50 = 7.50
        transaction.addItem(item2, 2); // 2 * 1.80 = 3.60

        // Calculate total
        transaction.calculateTotal(); // 11.10

        // Apply discount
        transaction.applyDiscount(10); // 10% discount

        // Process payment
        transaction.processPayment(20.00);

        // Create a bill
        Bill bill = new Bill("B001", transaction);

        // Generate formatted bill
        String formattedBill = bill.generateFormattedBill();

        // Verify the formatted bill contains key information
        assertTrue("Bill should contain store name", formattedBill.contains("SYNEX OUTLET STORE"));
        assertTrue("Bill should contain bill number", formattedBill.contains("Bill Number: B001"));
        assertTrue("Bill should contain transaction type", formattedBill.contains("Transaction Type: IN_STORE"));
        assertTrue("Bill should contain Rice", formattedBill.contains("Rice"));
        assertTrue("Bill should contain Flour", formattedBill.contains("Flour"));
        assertTrue("Bill should contain total amount", formattedBill.contains("11.10"));
        assertTrue("Bill should contain discount amount", formattedBill.contains("1.11"));
        assertTrue("Bill should contain final amount", formattedBill.contains("9.99"));
        assertTrue("Bill should contain cash tendered", formattedBill.contains("20.00"));
        assertTrue("Bill should contain change", formattedBill.contains("10.01"));
        assertTrue("Bill should contain thank you message", formattedBill.contains("Thank you for shopping with us!"));
    }

    /**
     * Tests the toString method.
     */
    @Test
    public void testToString() {
        // Create a transaction
        Transaction transaction = new Transaction("T001", null, TransactionType.IN_STORE);

        // Create a bill
        Bill bill = new Bill("B001", transaction);

        // Verify the string representation
        String str = bill.toString();
        assertTrue(str.contains("B001"));
        assertTrue(str.contains("T001"));
    }
}
