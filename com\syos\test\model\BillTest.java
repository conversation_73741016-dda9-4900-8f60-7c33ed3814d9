package com.syos.test.model;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.model.Bill;
import java.time.LocalDateTime;

/**
 * Unit tests for Bill model class
 */
public class BillTest {
    
    private Bill bill;
    
    @Before
    public void setUp() {
        bill = new Bill("B001", "T001", 15.75, 20.00, 4.25);
    }
    
    @Test
    public void testBillCreation() {
        assertNotNull("Bill should not be null", bill);
        assertEquals("Bill ID should match", "B001", bill.getId());
        assertEquals("Transaction ID should match", "T001", bill.getTransactionId());
        assertEquals("Total amount should match", 15.75, bill.getTotalAmount(), 0.01);
        assertEquals("Cash tendered should match", 20.00, bill.getCashTendered(), 0.01);
        assertEquals("Change should match", 4.25, bill.getChange(), 0.01);
    }
    
    @Test
    public void testBillTimestamp() {
        LocalDateTime beforeCreation = LocalDateTime.now().minusSeconds(1);
        Bill newBill = new Bill("B002", "T002", 10.00, 10.00, 0.00);
        LocalDateTime afterCreation = LocalDateTime.now().plusSeconds(1);
        
        assertNotNull("Timestamp should be set", newBill.getTimestamp());
        assertTrue("Timestamp should be recent", 
            newBill.getTimestamp().isAfter(beforeCreation) && 
            newBill.getTimestamp().isBefore(afterCreation));
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidBillId() {
        new Bill(null, "T001", 15.75, 20.00, 4.25);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testNegativeAmount() {
        new Bill("B003", "T003", -1.0, 20.00, 4.25);
    }
}
