@echo off

echo Starting SYOS with MySQL Database...

REM Set MySQL database configuration (you can modify these)
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=syos_db
set DB_USERNAME=root
set DB_PASSWORD=

REM Set storage type to MySQL
set STORAGE_TYPE=mysql

REM Create necessary directories
if not exist bin mkdir bin
if not exist reports mkdir reports

echo Compiling application...
javac -d bin -cp "lib/mysql-connector-java-8.0.33.jar" com/syos/config/*.java com/syos/util/*.java com/syos/model/*.java com/syos/repository/*.java com/syos/repository/impl/*.java com/syos/factory/*.java com/syos/service/*.java com/syos/ui/*.java com/syos/*.java

if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Running SYOS with MySQL database...
java -cp "bin;lib/mysql-connector-java-8.0.33.jar" ^
     -Dstorage.type=mysql ^
     -Ddb.host=%DB_HOST% ^
     -Ddb.port=%DB_PORT% ^
     -Ddb.name=%DB_NAME% ^
     -Ddb.username=%DB_USERNAME% ^
     -Ddb.password=%DB_PASSWORD% ^
     com.syos.SyosApplication

pause
