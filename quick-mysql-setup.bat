@echo off
echo ========================================
echo SYOS MySQL Quick Setup Script
echo ========================================
echo.

REM Check if MySQL is installed
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: MySQL is not installed or not in PATH
    echo Please install MySQL first using the guide in MYSQL-COMPLETE-SETUP-GUIDE.md
    echo.
    pause
    exit /b 1
)

echo MySQL found! Proceeding with setup...
echo.

REM Get MySQL credentials
set /p MYSQL_USER="Enter MySQL username (default: root): "
if "%MYSQL_USER%"=="" set MYSQL_USER=root

set /p MYSQL_PASSWORD="Enter MySQL password: "
if "%MYSQL_PASSWORD%"=="" (
    echo ERROR: Password cannot be empty
    pause
    exit /b 1
)

echo.
echo Testing MySQL connection...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Cannot connect to MySQL with provided credentials
    echo Please check your username and password
    pause
    exit /b 1
)

echo MySQL connection successful!
echo.

REM Create database
echo Creating database 'syos_db'...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS syos_db;"
if %errorlevel% neq 0 (
    echo ERROR: Failed to create database
    pause
    exit /b 1
)

echo Database created successfully!
echo.

REM Run setup scripts
echo Setting up database schema...
if exist "database\setup.sql" (
    mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% syos_db < database\setup.sql
    if %errorlevel% neq 0 (
        echo ERROR: Failed to setup database schema
        pause
        exit /b 1
    )
    echo Database schema created successfully!
) else (
    echo WARNING: database\setup.sql not found, skipping schema setup
)

echo.
echo Loading sample data...
if exist "database\sample_data.sql" (
    mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% syos_db < database\sample_data.sql
    if %errorlevel% neq 0 (
        echo WARNING: Failed to load sample data (this is optional)
    ) else (
        echo Sample data loaded successfully!
    )
) else (
    echo WARNING: database\sample_data.sql not found, skipping sample data
)

echo.
echo Updating .env file with your credentials...
(
echo # SYOS Database Configuration
echo # Updated by quick-mysql-setup.bat
echo.
echo # Database Connection Settings
echo DB_HOST=localhost
echo DB_PORT=3306
echo DB_NAME=syos_db
echo.
echo # Database Credentials
echo DB_USERNAME=%MYSQL_USER%
echo DB_PASSWORD=%MYSQL_PASSWORD%
echo.
echo # Connection Settings
echo ENABLE_SSL=false
echo ALLOW_PUBLIC_KEY_RETRIEVAL=true
) > .env

echo .env file updated successfully!
echo.

REM Verify setup
echo Verifying database setup...
mysql -u %MYSQL_USER% -p%MYSQL_PASSWORD% syos_db -e "SHOW TABLES;" >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Could not verify database tables
) else (
    echo Database verification successful!
)

echo.
echo ========================================
echo MySQL Setup Complete!
echo ========================================
echo.
echo You can now run the application with:
echo   run-mysql.bat
echo   or
echo   run.bat
echo.
echo Configuration saved to .env file
echo Database: syos_db
echo Username: %MYSQL_USER%
echo.
pause
