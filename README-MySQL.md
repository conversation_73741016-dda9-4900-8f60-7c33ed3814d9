# SYOS MySQL Database Integration

This document describes how to set up and use the Synex Outlet Store (SYOS) system with MySQL database instead of file-based storage.

## Prerequisites

1. **MySQL Server** (version 5.7 or higher)
2. **Java 8 or higher**
3. **MySQL JDBC Driver** (included in `lib/mysql-connector-java-8.0.33.jar`)

## Database Setup

### Option 1: Automatic Setup (Recommended)
The application will automatically create the database and tables when you run it for the first time with MySQL configuration.

### Option 2: Manual Setup
1. Start MySQL server
2. Connect to MySQL as root or admin user
3. Run the setup script:
   ```sql
   mysql -u root -p < database/setup.sql
   ```
4. Optionally, load sample data:
   ```sql
   mysql -u root -p < database/sample_data.sql
   ```

## Configuration

### Database Connection Settings
You can configure the database connection using system properties or environment variables:

| Property | Default Value | Description |
|----------|---------------|-------------|
| `db.host` | localhost | MySQL server hostname |
| `db.port` | 3306 | MySQL server port |
| `db.name` | syos_db | Database name |
| `db.username` | root | MySQL username |
| `db.password` | (empty) | MySQL password |

### Storage Type Selection
Set the storage type using the system property:
- `storage.type=mysql` - Use MySQL database
- `storage.type=file` - Use file-based storage (default fallback)

## Running the Application

### Windows
```batch
# Run with MySQL (modify database credentials in run-mysql.bat)
run-mysql.bat

# Run with file storage
run-file.bat
```

### Linux/Mac
```bash
# Run with MySQL (modify database credentials in run-mysql.sh)
chmod +x run-mysql.sh
./run-mysql.sh

# Run with file storage
chmod +x run.sh
./run.sh
```

### Manual Execution
```bash
# Compile
javac -d bin -cp "lib/mysql-connector-java-8.0.33.jar" com/syos/**/*.java

# Run with MySQL
java -cp "bin:lib/mysql-connector-java-8.0.33.jar" \
     -Dstorage.type=mysql \
     -Ddb.host=localhost \
     -Ddb.port=3306 \
     -Ddb.name=syos_db \
     -Ddb.username=root \
     -Ddb.password=yourpassword \
     com.syos.SyosApplication

# Run with file storage
java -cp "bin" -Dstorage.type=file com.syos.SyosApplication
```

## Database Schema

### Tables Created
1. **items** - Product information
2. **users** - User accounts
3. **stock_batches** - Inventory batches
4. **transactions** - Transaction records
5. **transaction_items** - Cart items (many-to-many)
6. **bills** - Bill information

### Key Features
- **Foreign Key Constraints** - Ensures data integrity
- **Indexes** - Optimized for common queries
- **UTF-8 Support** - Full Unicode character support
- **Timestamps** - Automatic created/updated timestamps

## Migration from File Storage

### Data Migration
To migrate existing file-based data to MySQL:

1. Start the application with file storage
2. Export your data (if needed)
3. Set up MySQL database
4. Restart application with MySQL configuration
5. Re-enter data or use the sample data script

### Switching Between Storage Types
The application supports switching between storage types by changing the `storage.type` system property. No code changes are required.

## Testing with MySQL

### Running Tests with MySQL
```bash
# Run tests with MySQL database
java -cp "bin:lib/junit-4.13.2.jar:lib/mockito-core-3.12.4.jar:lib/hamcrest-core-1.3.jar:lib/byte-buddy-1.11.13.jar:lib/objenesis-3.2.jar:lib/mysql-connector-java-8.0.33.jar" \
     -Dtest.storage.type=mysql \
     -Ddb.name=syos_test_db \
     org.junit.runner.JUnitCore com.syos.test.SyosTestSuite
```

### Test Database
Tests use a separate database (`syos_test_db`) to avoid interfering with production data.

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check MySQL server is running
   - Verify connection credentials
   - Check firewall settings

2. **Database Not Found**
   - Ensure database exists or let application create it
   - Check database name in configuration

3. **Permission Denied**
   - Verify user has necessary privileges
   - Grant permissions: `GRANT ALL PRIVILEGES ON syos_db.* TO 'username'@'localhost';`

4. **Driver Not Found**
   - Ensure `mysql-connector-java-8.0.33.jar` is in the `lib` directory
   - Check classpath includes the MySQL driver

### Fallback Behavior
If MySQL connection fails, the application automatically falls back to file-based storage with a warning message.

## Performance Considerations

### Optimizations
- Indexes on frequently queried columns
- Connection pooling (for production use)
- Prepared statements to prevent SQL injection
- Batch operations for bulk inserts

### Monitoring
- Monitor database connection count
- Check query performance with `EXPLAIN`
- Regular database maintenance (OPTIMIZE TABLE)

## Security

### Best Practices
- Use strong passwords
- Create dedicated database user with minimal privileges
- Enable SSL connections in production
- Regular security updates

### Database User Setup
```sql
CREATE USER 'syos_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX ON syos_db.* TO 'syos_user'@'localhost';
FLUSH PRIVILEGES;
```

## Architecture Benefits

### MySQL Integration Benefits
1. **ACID Compliance** - Data consistency and reliability
2. **Concurrent Access** - Multiple users can access simultaneously
3. **Scalability** - Better performance with large datasets
4. **Backup/Recovery** - Standard database backup tools
5. **Reporting** - SQL queries for complex reports
6. **Data Integrity** - Foreign key constraints and validation

### Design Patterns Used
- **Repository Pattern** - Abstracted data access
- **Factory Pattern** - Pluggable storage implementations
- **Dependency Injection** - Loose coupling between layers
- **Strategy Pattern** - Switchable storage strategies
