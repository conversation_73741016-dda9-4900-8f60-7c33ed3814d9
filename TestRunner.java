import com.syos.model.*;
import java.time.LocalDate;

/**
 * Simple test runner to verify our model classes work
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("SYOS Model Classes Test Runner");
        System.out.println("========================================");
        
        try {
            testItemClass();
            testStockBatchClass();
            testUserClass();
            
            System.out.println("\n✅ ALL TESTS PASSED! ✅");
            
        } catch (Exception e) {
            System.out.println("\n❌ TEST FAILED: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("========================================");
    }
    
    private static void testItemClass() {
        System.out.println("\n🧪 Testing Item class...");
        
        // Test valid item creation
        Item item = new Item("I001", "Rice", 2.50, "Groceries");
        assert item.getCode().equals("I001") : "Item code should match";
        assert item.getName().equals("Rice") : "Item name should match";
        assert item.getPrice() == 2.50 : "Item price should match";
        assert item.getCategory().equals("Groceries") : "Item category should match";
        
        // Test validation methods
        assert Item.isValidCode("I001") : "Valid code should be accepted";
        assert !Item.isValidCode(null) : "Null code should be invalid";
        assert !Item.isValidCode("") : "Empty code should be invalid";
        assert Item.isValidPrice(1.0) : "Positive price should be valid";
        assert !Item.isValidPrice(-1.0) : "Negative price should be invalid";
        
        // Test invalid item creation
        try {
            new Item(null, "Test", 1.0, "Category");
            assert false : "Should throw exception for null code";
        } catch (IllegalArgumentException e) {
            // Expected
        }
        
        System.out.println("   ✅ Item class tests passed");
    }
    
    private static void testStockBatchClass() {
        System.out.println("\n🧪 Testing StockBatch class...");
        
        LocalDate receivedDate = LocalDate.now();
        LocalDate expiryDate = LocalDate.now().plusDays(30);
        
        StockBatch batch = new StockBatch("SB001", "I001", 100, receivedDate, expiryDate, StockType.SHELF);
        
        assert batch.getBatchId().equals("SB001") : "Batch ID should match";
        assert batch.getItemCode().equals("I001") : "Item code should match";
        assert batch.getQuantity() == 100 : "Quantity should match";
        assert batch.getStockType() == StockType.SHELF : "Stock type should match";
        
        // Test expiry methods
        assert !batch.isExpired() : "Future expiry should not be expired";
        assert !batch.isExpiringSoon() : "30 days should not be expiring soon";
        
        // Test with expiring soon batch
        StockBatch soonExpiry = new StockBatch("SB002", "I001", 50, 
            LocalDate.now(), LocalDate.now().plusDays(2), StockType.SHELF);
        assert soonExpiry.isExpiringSoon() : "2 days should be expiring soon";
        
        System.out.println("   ✅ StockBatch class tests passed");
    }
    
    private static void testUserClass() {
        System.out.println("\n🧪 Testing User class...");
        
        User user = new User("U001", "testuser", "password123", "Test User", 
            "<EMAIL>", "123 Test St", "1234567890", true);
        
        assert user.getId().equals("U001") : "User ID should match";
        assert user.getUsername().equals("testuser") : "Username should match";
        assert user.isOnlineCustomer() : "Should be online customer";
        
        // Test password validation
        assert user.validatePassword("password123") : "Correct password should validate";
        assert !user.validatePassword("wrongpassword") : "Wrong password should not validate";
        
        // Test email validation
        assert User.isValidEmail("<EMAIL>") : "Valid email should pass";
        assert !User.isValidEmail("invalid-email") : "Invalid email should fail";
        assert !User.isValidEmail(null) : "Null email should fail";
        
        // Test invalid user creation
        try {
            new User(null, "test", "pass", "Name", "<EMAIL>", "Address", "Phone", true);
            assert false : "Should throw exception for null user ID";
        } catch (IllegalArgumentException e) {
            // Expected
        }
        
        try {
            new User("U002", "test", "pass", "Name", "invalid-email", "Address", "Phone", true);
            assert false : "Should throw exception for invalid email";
        } catch (IllegalArgumentException e) {
            // Expected
        }
        
        System.out.println("   ✅ User class tests passed");
    }
}
