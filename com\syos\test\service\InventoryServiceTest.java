package com.syos.test.service;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.service.InventoryService;
import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.factory.ServiceFactory;
import java.time.LocalDate;
import java.util.List;

/**
 * Unit tests for InventoryService
 */
public class InventoryServiceTest {
    
    private InventoryService inventoryService;
    
    @Before
    public void setUp() {
        // Use file-based repositories for testing
        System.setProperty("storage.type", "file");
        inventoryService = ServiceFactory.createInventoryService();
    }
    
    @Test
    public void testAddItem() {
        Item item = new Item("TEST001", "Test Item", 1.99, "Test Category");
        
        boolean result = inventoryService.addItem(item);
        assertTrue("Item should be added successfully", result);
        
        // Verify item was added
        Item retrievedItem = inventoryService.getItem("TEST001");
        assertNotNull("Retrieved item should not be null", retrievedItem);
        assertEquals("Item codes should match", "TEST001", retrievedItem.getCode());
    }
    
    @Test
    public void testAddDuplicateItem() {
        Item item1 = new Item("TEST002", "Test Item 1", 1.99, "Test Category");
        Item item2 = new Item("TEST002", "Test Item 2", 2.99, "Test Category");
        
        assertTrue("First item should be added", inventoryService.addItem(item1));
        assertFalse("Duplicate item should not be added", inventoryService.addItem(item2));
    }
    
    @Test
    public void testAddStockBatch() {
        // First add an item
        Item item = new Item("TEST003", "Test Item", 1.99, "Test Category");
        inventoryService.addItem(item);
        
        // Then add stock batch
        StockBatch batch = new StockBatch("SB001", "TEST003", 100, 
            LocalDate.now(), LocalDate.now().plusDays(30), StockType.SHELF);
        
        boolean result = inventoryService.addStockBatch(batch);
        assertTrue("Stock batch should be added successfully", result);
    }
    
    @Test
    public void testAddStockBatchForNonExistentItem() {
        StockBatch batch = new StockBatch("SB002", "NONEXISTENT", 100, 
            LocalDate.now(), LocalDate.now().plusDays(30), StockType.SHELF);
        
        boolean result = inventoryService.addStockBatch(batch);
        assertFalse("Stock batch for non-existent item should not be added", result);
    }
    
    @Test
    public void testGetAllItems() {
        // Add some test items
        inventoryService.addItem(new Item("TEST004", "Test Item 4", 1.99, "Category A"));
        inventoryService.addItem(new Item("TEST005", "Test Item 5", 2.99, "Category B"));
        
        List<Item> items = inventoryService.getAllItems();
        assertNotNull("Items list should not be null", items);
        assertTrue("Items list should contain added items", items.size() >= 2);
    }
    
    @Test
    public void testGetItemStock() {
        // Add item and stock
        Item item = new Item("TEST006", "Test Item", 1.99, "Test Category");
        inventoryService.addItem(item);
        
        StockBatch batch1 = new StockBatch("SB003", "TEST006", 50, 
            LocalDate.now(), LocalDate.now().plusDays(30), StockType.SHELF);
        StockBatch batch2 = new StockBatch("SB004", "TEST006", 30, 
            LocalDate.now(), LocalDate.now().plusDays(20), StockType.STORAGE);
        
        inventoryService.addStockBatch(batch1);
        inventoryService.addStockBatch(batch2);
        
        List<StockBatch> stock = inventoryService.getItemStock("TEST006");
        assertNotNull("Stock list should not be null", stock);
        assertEquals("Should have 2 stock batches", 2, stock.size());
    }
    
    @Test
    public void testMoveItemsToShelf() {
        // Add item and storage stock
        Item item = new Item("TEST007", "Test Item", 1.99, "Test Category");
        inventoryService.addItem(item);
        
        StockBatch storageBatch = new StockBatch("SB005", "TEST007", 100, 
            LocalDate.now(), LocalDate.now().plusDays(30), StockType.STORAGE);
        inventoryService.addStockBatch(storageBatch);
        
        // Move 50 items to shelf
        boolean result = inventoryService.moveItemsToShelf("TEST007", 50);
        assertTrue("Items should be moved to shelf successfully", result);
        
        // Verify the move
        List<StockBatch> stock = inventoryService.getItemStock("TEST007");
        boolean hasShelfStock = stock.stream()
            .anyMatch(batch -> batch.getStockType() == StockType.SHELF && batch.getQuantity() == 50);
        assertTrue("Should have shelf stock of 50", hasShelfStock);
    }
    
    @Test
    public void testMoveMoreItemsThanAvailable() {
        // Add item and limited storage stock
        Item item = new Item("TEST008", "Test Item", 1.99, "Test Category");
        inventoryService.addItem(item);
        
        StockBatch storageBatch = new StockBatch("SB006", "TEST008", 30, 
            LocalDate.now(), LocalDate.now().plusDays(30), StockType.STORAGE);
        inventoryService.addStockBatch(storageBatch);
        
        // Try to move more than available
        boolean result = inventoryService.moveItemsToShelf("TEST008", 50);
        assertFalse("Should not be able to move more items than available", result);
    }
}
