package com.syos.test.model;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.model.CartItem;

/**
 * Unit tests for CartItem model class
 */
public class CartItemTest {
    
    private CartItem cartItem;
    
    @Before
    public void setUp() {
        cartItem = new CartItem("I001", 3, 2.50);
    }
    
    @Test
    public void testCartItemCreation() {
        assertNotNull("CartItem should not be null", cartItem);
        assertEquals("Item code should match", "I001", cartItem.getItemCode());
        assertEquals("Quantity should match", 3, cartItem.getQuantity());
        assertEquals("Unit price should match", 2.50, cartItem.getUnitPrice(), 0.01);
    }
    
    @Test
    public void testCalculateSubtotal() {
        double subtotal = cartItem.getSubtotal();
        assertEquals("Subtotal should be calculated correctly", 7.50, subtotal, 0.01);
    }
    
    @Test
    public void testQuantityUpdate() {
        cartItem.setQuantity(5);
        assertEquals("Quantity should be updated", 5, cartItem.getQuantity());
        assertEquals("Subtotal should be recalculated", 12.50, cartItem.getSubtotal(), 0.01);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidQuantity() {
        new CartItem("I001", 0, 2.50);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidPrice() {
        new CartItem("I001", 3, -1.0);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testNullItemCode() {
        new CartItem(null, 3, 2.50);
    }
}
