package com.syos.test.model;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.model.StockBatch;
import com.syos.model.StockType;
import java.time.LocalDate;

/**
 * Unit tests for StockBatch model class
 */
public class StockBatchTest {
    
    private StockBatch stockBatch;
    private LocalDate receivedDate;
    private LocalDate expiryDate;
    
    @Before
    public void setUp() {
        receivedDate = LocalDate.now();
        expiryDate = LocalDate.now().plusDays(30);
        stockBatch = new StockBatch("SB001", "I001", 100, receivedDate, expiryDate, StockType.SHELF);
    }
    
    @Test
    public void testStockBatchCreation() {
        assertNotNull("StockBatch should not be null", stockBatch);
        assertEquals("Batch ID should match", "SB001", stockBatch.getBatchId());
        assertEquals("Item code should match", "I001", stockBatch.getItemCode());
        assertEquals("Quantity should match", 100, stockBatch.getQuantity());
        assertEquals("Received date should match", receivedDate, stockBatch.getReceivedDate());
        assertEquals("Expiry date should match", expiryDate, stockBatch.getExpiryDate());
        assertEquals("Stock type should match", StockType.SHELF, stockBatch.getStockType());
    }
    
    @Test
    public void testQuantityUpdate() {
        stockBatch.setQuantity(50);
        assertEquals("Quantity should be updated", 50, stockBatch.getQuantity());
    }
    
    @Test
    public void testStockTypeUpdate() {
        stockBatch.setStockType(StockType.STORAGE);
        assertEquals("Stock type should be updated", StockType.STORAGE, stockBatch.getStockType());
    }
    
    @Test
    public void testIsExpired() {
        // Test with future expiry date
        StockBatch futureExpiry = new StockBatch("SB002", "I001", 100, 
            LocalDate.now(), LocalDate.now().plusDays(10), StockType.SHELF);
        assertFalse("Batch with future expiry should not be expired", futureExpiry.isExpired());
        
        // Test with past expiry date
        StockBatch pastExpiry = new StockBatch("SB003", "I001", 100, 
            LocalDate.now().minusDays(20), LocalDate.now().minusDays(1), StockType.SHELF);
        assertTrue("Batch with past expiry should be expired", pastExpiry.isExpired());
    }
    
    @Test
    public void testIsExpiringSoon() {
        // Test with expiry in 2 days (should be expiring soon)
        StockBatch soonExpiry = new StockBatch("SB004", "I001", 100, 
            LocalDate.now(), LocalDate.now().plusDays(2), StockType.SHELF);
        assertTrue("Batch expiring in 2 days should be expiring soon", soonExpiry.isExpiringSoon());
        
        // Test with expiry in 10 days (should not be expiring soon)
        StockBatch laterExpiry = new StockBatch("SB005", "I001", 100, 
            LocalDate.now(), LocalDate.now().plusDays(10), StockType.SHELF);
        assertFalse("Batch expiring in 10 days should not be expiring soon", laterExpiry.isExpiringSoon());
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidQuantity() {
        new StockBatch("SB001", "I001", -1, receivedDate, expiryDate, StockType.SHELF);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testNullBatchId() {
        new StockBatch(null, "I001", 100, receivedDate, expiryDate, StockType.SHELF);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidDateOrder() {
        // Expiry date before received date
        new StockBatch("SB001", "I001", 100, LocalDate.now(), LocalDate.now().minusDays(1), StockType.SHELF);
    }
}
