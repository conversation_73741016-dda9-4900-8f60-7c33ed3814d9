package com.syos.test.model;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.test.base.BaseTest;
import org.junit.Test;

import java.time.LocalDate;

import static org.junit.Assert.*;

/**
 * Unit tests for the StockBatch model class.
 */
public class StockBatchTest extends BaseTest {

    /**
     * Tests the constructor and getters.
     */
    @Test
    public void testConstructorAndGetters() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");

        // Create dates
        LocalDate receivedDate = LocalDate.now().minusDays(10);
        LocalDate expiryDate = LocalDate.now().plusMonths(6);

        // Create a stock batch
        StockBatch batch = new StockBatch("SB001", item, 100, receivedDate, expiryDate, StockType.SHELF);

        // Verify the values
        assertEquals("SB001", batch.getBatchId());
        assertEquals(item, batch.getItem());
        assertEquals(100, batch.getQuantity());
        assertEquals(receivedDate, batch.getReceivedDate());
        assertEquals(expiryDate, batch.getExpiryDate());
        assertEquals(StockType.SHELF, batch.getStockType());
    }

    /**
     * Tests the setters.
     */
    @Test
    public void testSetters() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");

        // Create dates
        LocalDate receivedDate = LocalDate.now().minusDays(10);
        LocalDate expiryDate = LocalDate.now().plusMonths(6);

        // Create a stock batch
        StockBatch batch = new StockBatch();

        // Set values
        batch.setBatchId("SB001");
        batch.setItem(item);
        batch.setQuantity(100);
        batch.setReceivedDate(receivedDate);
        batch.setExpiryDate(expiryDate);
        batch.setStockType(StockType.SHELF);

        // Verify the values
        assertEquals("SB001", batch.getBatchId());
        assertEquals(item, batch.getItem());
        assertEquals(100, batch.getQuantity());
        assertEquals(receivedDate, batch.getReceivedDate());
        assertEquals(expiryDate, batch.getExpiryDate());
        assertEquals(StockType.SHELF, batch.getStockType());
    }

    /**
     * Tests the decreaseQuantity method.
     */
    @Test
    public void testDecreaseQuantity() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");

        // Create dates
        LocalDate receivedDate = LocalDate.now().minusDays(10);
        LocalDate expiryDate = LocalDate.now().plusMonths(6);

        // Create a stock batch
        StockBatch batch = new StockBatch("SB001", item, 100, receivedDate, expiryDate, StockType.SHELF);

        // Test valid decrease
        boolean result1 = batch.decreaseQuantity(30);
        assertTrue(result1);
        assertEquals(70, batch.getQuantity());

        // Test invalid decrease (negative amount)
        boolean result2 = batch.decreaseQuantity(-10);
        assertFalse(result2);
        assertEquals(70, batch.getQuantity());

        // Test invalid decrease (amount > quantity)
        boolean result3 = batch.decreaseQuantity(80);
        assertFalse(result3);
        assertEquals(70, batch.getQuantity());
    }

    /**
     * Tests the increaseQuantity method.
     */
    @Test
    public void testIncreaseQuantity() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");

        // Create dates
        LocalDate receivedDate = LocalDate.now().minusDays(10);
        LocalDate expiryDate = LocalDate.now().plusMonths(6);

        // Create a stock batch
        StockBatch batch = new StockBatch("SB001", item, 100, receivedDate, expiryDate, StockType.SHELF);

        // Test valid increase
        batch.increaseQuantity(50);
        assertEquals(150, batch.getQuantity());

        // Test invalid increase (negative amount)
        batch.increaseQuantity(-10);
        assertEquals(150, batch.getQuantity()); // Should not change
    }

    /**
     * Tests the isExpired method.
     */
    @Test
    public void testIsExpired() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");

        // Create a batch that is not expired
        LocalDate futureDate = LocalDate.now().plusDays(10);
        StockBatch validBatch = new StockBatch("SB001", item, 100, LocalDate.now(), futureDate, StockType.SHELF);

        // Create a batch that is expired
        LocalDate pastDate = LocalDate.now().minusDays(10);
        StockBatch expiredBatch = new StockBatch("SB002", item, 100, LocalDate.now().minusDays(20), pastDate,
                StockType.SHELF);

        // Verify expiration
        assertFalse(validBatch.isExpired());
        assertTrue(expiredBatch.isExpired());
    }

    /**
     * Tests the daysUntilExpiry method.
     */
    @Test
    public void testDaysUntilExpiry() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");

        // Create a batch with a future expiry date
        LocalDate expiryDate = LocalDate.now().plusDays(10);
        StockBatch batch = new StockBatch("SB001", item, 100, LocalDate.now(), expiryDate, StockType.SHELF);

        // Verify days until expiry
        assertEquals(10, batch.daysUntilExpiry());

        // Create a batch with a past expiry date
        LocalDate pastDate = LocalDate.now().minusDays(10);
        StockBatch expiredBatch = new StockBatch("SB002", item, 100, LocalDate.now().minusDays(20), pastDate,
                StockType.SHELF);

        // Verify days until expiry (should be negative)
        assertEquals(-10, expiredBatch.daysUntilExpiry());
    }

    /**
     * Tests the toString method.
     */
    @Test
    public void testToString() {
        // Create an item
        Item item = new Item("I001", "Rice", 2.50, "Groceries");

        // Create dates
        LocalDate receivedDate = LocalDate.of(2023, 1, 1);
        LocalDate expiryDate = LocalDate.of(2023, 7, 1);

        // Create a stock batch
        StockBatch batch = new StockBatch("SB001", item, 100, receivedDate, expiryDate, StockType.SHELF);

        // Verify the string representation
        String expected = "StockBatch{batchId='SB001', item=" + item + ", quantity=100, receivedDate=" +
                receivedDate + ", expiryDate=" + expiryDate + ", stockType=SHELF}";
        assertEquals(expected, batch.toString());
    }
}
