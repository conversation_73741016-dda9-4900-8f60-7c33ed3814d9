#!/bin/bash

echo "========================================"
echo "SYOS MySQL Database Setup"
echo "========================================"

echo
echo "This script will help you set up the MySQL database for SYOS."
echo

echo "Step 1: Checking MySQL installation..."
if ! command -v mysql &> /dev/null; then
    echo "ERROR: MySQL is not installed or not in PATH."
    echo "Please install MySQL:"
    echo "  Ubuntu/Debian: sudo apt install mysql-server"
    echo "  CentOS/RHEL: sudo yum install mysql-server"
    echo "  macOS: brew install mysql"
    echo
    exit 1
fi
echo "MySQL is installed."

echo
echo "Step 2: Setting up database..."
echo "Please enter your MySQL root password when prompted."
echo

mysql -u root -p -e "source database/setup.sql"
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to create database."
    echo "Please check your MySQL credentials and try again."
    exit 1
fi

echo
echo "Step 3: Loading sample data (optional)..."
read -p "Do you want to load sample data? (y/n): " load_sample
if [[ $load_sample =~ ^[Yy]$ ]]; then
    mysql -u root -p -e "source database/sample_data.sql"
    if [ $? -ne 0 ]; then
        echo "WARNING: Failed to load sample data."
        echo "You can load it manually later."
    else
        echo "Sample data loaded successfully."
    fi
fi

echo
echo "========================================"
echo "Database setup complete!"
echo "========================================"
echo
echo "You can now run the SYOS application with:"
echo "  ./run-mysql.sh"
echo
echo "Or simply:"
echo "  java -cp \"bin:lib/mysql-connector-java-5.1.49.jar\" com.syos.SyosApplication"
echo
