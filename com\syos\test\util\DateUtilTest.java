package com.syos.test.util;

import org.junit.Test;
import static org.junit.Assert.*;

import com.syos.util.DateUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Unit tests for DateUtil class
 */
public class DateUtilTest {
    
    @Test
    public void testFormatDate() {
        LocalDate date = LocalDate.of(2025, 1, 15);
        String formatted = DateUtil.formatDate(date);
        assertEquals("Date should be formatted correctly", "2025-01-15", formatted);
    }
    
    @Test
    public void testFormatDateTime() {
        LocalDateTime dateTime = LocalDateTime.of(2025, 1, 15, 14, 30, 45);
        String formatted = DateUtil.formatDateTime(dateTime);
        assertEquals("DateTime should be formatted correctly", "2025-01-15 14:30:45", formatted);
    }
    
    @Test
    public void testParseDate() {
        LocalDate parsed = DateUtil.parseDate("2025-01-15");
        assertEquals("Parsed date should match", LocalDate.of(2025, 1, 15), parsed);
    }
    
    @Test
    public void testParseDateInvalidFormat() {
        LocalDate parsed = DateUtil.parseDate("invalid-date");
        assertNull("Invalid date should return null", parsed);
    }
    
    @Test
    public void testIsValidDateFormat() {
        assertTrue("Valid date format should return true", DateUtil.isValidDateFormat("2025-01-15"));
        assertFalse("Invalid date format should return false", DateUtil.isValidDateFormat("15/01/2025"));
        assertFalse("Invalid date format should return false", DateUtil.isValidDateFormat("2025-13-01"));
    }
    
    @Test
    public void testDaysBetween() {
        LocalDate date1 = LocalDate.of(2025, 1, 1);
        LocalDate date2 = LocalDate.of(2025, 1, 11);
        
        long days = DateUtil.daysBetween(date1, date2);
        assertEquals("Days between should be calculated correctly", 10, days);
    }
    
    @Test
    public void testIsExpired() {
        LocalDate pastDate = LocalDate.now().minusDays(1);
        LocalDate futureDate = LocalDate.now().plusDays(1);
        
        assertTrue("Past date should be expired", DateUtil.isExpired(pastDate));
        assertFalse("Future date should not be expired", DateUtil.isExpired(futureDate));
    }
    
    @Test
    public void testIsExpiringSoon() {
        LocalDate soonDate = LocalDate.now().plusDays(2);
        LocalDate laterDate = LocalDate.now().plusDays(10);
        
        assertTrue("Date in 2 days should be expiring soon", DateUtil.isExpiringSoon(soonDate));
        assertFalse("Date in 10 days should not be expiring soon", DateUtil.isExpiringSoon(laterDate));
    }
}
