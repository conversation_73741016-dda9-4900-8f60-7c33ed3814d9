package com.syos.test.service;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.service.BillingService;
import com.syos.service.InventoryService;
import com.syos.model.*;
import com.syos.factory.ServiceFactory;
import java.time.LocalDate;
import java.util.List;
import java.util.ArrayList;

/**
 * Unit tests for BillingService
 */
public class BillingServiceTest {
    
    private BillingService billingService;
    private InventoryService inventoryService;
    
    @Before
    public void setUp() {
        // Use file-based repositories for testing
        System.setProperty("storage.type", "file");
        billingService = ServiceFactory.createBillingService();
        inventoryService = ServiceFactory.createInventoryService();
        
        // Set up test data
        setupTestData();
    }
    
    private void setupTestData() {
        // Add test items
        Item item1 = new Item("BILL001", "Test Bread", 2.50, "Bakery");
        Item item2 = new Item("BILL002", "Test Milk", 3.00, "Dairy");
        
        inventoryService.addItem(item1);
        inventoryService.addItem(item2);
        
        // Add stock for items
        StockBatch batch1 = new StockBatch("SB_BILL001", "BILL001", 50, 
            LocalDate.now(), LocalDate.now().plusDays(5), StockType.SHELF);
        StockBatch batch2 = new StockBatch("SB_BILL002", "BILL002", 30, 
            LocalDate.now(), LocalDate.now().plusDays(7), StockType.SHELF);
        
        inventoryService.addStockBatch(batch1);
        inventoryService.addStockBatch(batch2);
    }
    
    @Test
    public void testCreateInStoreTransaction() {
        List<CartItem> cartItems = new ArrayList<>();
        cartItems.add(new CartItem("BILL001", 2, 2.50));
        cartItems.add(new CartItem("BILL002", 1, 3.00));
        
        Transaction transaction = billingService.createInStoreTransaction(cartItems, 0.0);
        
        assertNotNull("Transaction should not be null", transaction);
        assertEquals("Transaction type should be IN_STORE", TransactionType.IN_STORE, transaction.getType());
        assertEquals("Total amount should be correct", 8.00, transaction.getTotalAmount(), 0.01);
        assertEquals("Cart items count should match", 2, transaction.getCartItems().size());
    }
    
    @Test
    public void testCreateTransactionWithDiscount() {
        List<CartItem> cartItems = new ArrayList<>();
        cartItems.add(new CartItem("BILL001", 2, 2.50)); // 5.00
        
        Transaction transaction = billingService.createInStoreTransaction(cartItems, 10.0); // 10% discount
        
        assertNotNull("Transaction should not be null", transaction);
        assertEquals("Total with discount should be correct", 4.50, transaction.getTotalAmount(), 0.01);
        assertEquals("Discount should be applied", 10.0, transaction.getDiscountPercentage(), 0.01);
    }
    
    @Test
    public void testCreateOnlineTransaction() {
        // First create a user
        User user = new User("U001", "testuser", "password", "Test User", 
            "<EMAIL>", "123 Test St", "1234567890", true);
        
        List<CartItem> cartItems = new ArrayList<>();
        cartItems.add(new CartItem("BILL001", 1, 2.50));
        
        Transaction transaction = billingService.createOnlineTransaction(cartItems, 0.0, user);
        
        assertNotNull("Transaction should not be null", transaction);
        assertEquals("Transaction type should be ONLINE", TransactionType.ONLINE, transaction.getType());
        assertEquals("User should be set", "U001", transaction.getUserId());
    }
    
    @Test
    public void testProcessTransaction() {
        List<CartItem> cartItems = new ArrayList<>();
        cartItems.add(new CartItem("BILL001", 2, 2.50));
        
        Transaction transaction = billingService.createInStoreTransaction(cartItems, 0.0);
        boolean result = billingService.processTransaction(transaction);
        
        assertTrue("Transaction should be processed successfully", result);
        assertNotNull("Transaction should have an ID", transaction.getId());
        assertNotNull("Transaction should have a timestamp", transaction.getTimestamp());
    }
    
    @Test
    public void testProcessTransactionInsufficientStock() {
        List<CartItem> cartItems = new ArrayList<>();
        // Try to buy more than available (we only have 50 in stock)
        cartItems.add(new CartItem("BILL001", 100, 2.50));
        
        Transaction transaction = billingService.createInStoreTransaction(cartItems, 0.0);
        boolean result = billingService.processTransaction(transaction);
        
        assertFalse("Transaction should fail due to insufficient stock", result);
    }
    
    @Test
    public void testGenerateBill() {
        List<CartItem> cartItems = new ArrayList<>();
        cartItems.add(new CartItem("BILL001", 2, 2.50));
        cartItems.add(new CartItem("BILL002", 1, 3.00));
        
        Transaction transaction = billingService.createInStoreTransaction(cartItems, 5.0); // 5% discount
        billingService.processTransaction(transaction);
        
        Bill bill = billingService.generateBill(transaction, 10.00); // Cash tendered
        
        assertNotNull("Bill should not be null", bill);
        assertEquals("Bill transaction ID should match", transaction.getId(), bill.getTransactionId());
        assertEquals("Cash tendered should match", 10.00, bill.getCashTendered(), 0.01);
        assertEquals("Change should be calculated correctly", 2.40, bill.getChange(), 0.01); // 10.00 - 7.60
    }
    
    @Test
    public void testCalculateChange() {
        double totalAmount = 15.75;
        double cashTendered = 20.00;
        
        double change = billingService.calculateChange(totalAmount, cashTendered);
        assertEquals("Change should be calculated correctly", 4.25, change, 0.01);
    }
    
    @Test
    public void testCalculateChangeInsufficientCash() {
        double totalAmount = 15.75;
        double cashTendered = 10.00;
        
        double change = billingService.calculateChange(totalAmount, cashTendered);
        assertEquals("Change should be negative for insufficient cash", -5.75, change, 0.01);
    }
    
    @Test
    public void testGetAllTransactions() {
        // Create and process a transaction
        List<CartItem> cartItems = new ArrayList<>();
        cartItems.add(new CartItem("BILL001", 1, 2.50));
        
        Transaction transaction = billingService.createInStoreTransaction(cartItems, 0.0);
        billingService.processTransaction(transaction);
        
        List<Transaction> transactions = billingService.getAllTransactions();
        assertNotNull("Transactions list should not be null", transactions);
        assertTrue("Should have at least one transaction", transactions.size() >= 1);
    }
}
