package com.syos.test.repository;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.repository.ItemRepository;
import com.syos.repository.impl.FileItemRepository;
import com.syos.model.Item;

/**
 * Unit tests for ItemRepository implementations
 */
public class ItemRepositoryTest {
    
    private ItemRepository itemRepository;
    
    @Before
    public void setUp() {
        itemRepository = new FileItemRepository();
    }
    
    @Test
    public void testSaveAndFindItem() {
        Item item = new Item("REPO_TEST001", "Repository Test Item", 1.99, "Test");
        
        boolean saved = itemRepository.save(item);
        assertTrue("Item should be saved successfully", saved);
        
        Item found = itemRepository.findByCode("REPO_TEST001");
        assertNotNull("Item should be found", found);
        assertEquals("Item code should match", "REPO_TEST001", found.getCode());
    }
    
    @Test
    public void testFindNonExistentItem() {
        Item found = itemRepository.findByCode("NONEXISTENT");
        assertNull("Non-existent item should return null", found);
    }
    
    
}
