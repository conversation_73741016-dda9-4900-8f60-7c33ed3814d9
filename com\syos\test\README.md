# SYOS Test Suite

This directory contains comprehensive unit and integration tests for the SYOS application.

## Test Structure

```
test/
├── model/              # Model class tests
├── repository/         # Repository layer tests
├── service/           # Service layer tests
├── util/              # Utility class tests
├── integration/       # Integration tests
└── base/              # Base test classes and utilities
```

## Running Tests

### Prerequisites
- JUnit 4.13.2
- Hamcrest Core 1.3
- Mockito 3.12.4 (for mocking)

### Compile Tests
```bash
javac -d bin -cp "bin;lib\junit-4.13.2.jar;lib\hamcrest-core-1.3.jar;lib\mockito-core-3.12.4.jar" com\syos\test\*.java com\syos\test\*\*.java
```

### Run All Tests
```bash
java -cp "bin;lib\junit-4.13.2.jar;lib\hamcrest-core-1.3.jar;lib\mockito-core-3.12.4.jar" org.junit.runner.JUnitCore com.syos.test.SyosTestSuite
```

### Run Individual Tests
```bash
java -cp "bin;lib\junit-4.13.2.jar;lib\hamcrest-core-1.3.jar" org.junit.runner.JUnitCore com.syos.test.model.ItemTest
```

## Test Coverage

- ✅ Model classes validation
- ✅ Repository CRUD operations
- ✅ Service business logic
- ✅ Utility functions
- ✅ Integration workflows
- ✅ Error handling scenarios
