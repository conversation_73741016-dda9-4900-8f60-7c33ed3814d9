-- SYOS Sample Data Script
-- This script inserts sample data for testing the system

USE syos_db;

-- Insert sample items
INSERT INTO items (code, name, price, category) VALUES
('I001', 'Rice (1kg)', 2.50, 'Groceries'),
('I002', 'Flour (1kg)', 1.80, 'Groceries'),
('I003', 'Sugar (1kg)', 1.50, 'Groceries'),
('I004', 'Milk (1L)', 1.20, 'Dairy'),
('I005', 'Eggs (12)', 2.00, 'Dairy'),
('I006', 'Bread', 1.00, 'Bakery'),
('I007', 'Chicken (1kg)', 5.00, 'Meat'),
('I008', 'Beef (1kg)', 7.50, 'Meat'),
('I009', 'Apples (1kg)', 2.20, 'Fruits'),
('I010', '<PERSON>ana<PERSON> (1kg)', 1.80, 'Fruits')
ON DUPLICATE KEY UPDATE 
name = VALUES(name), 
price = VALUES(price), 
category = VALUES(category);

-- Insert sample users
INSERT INTO users (user_id, username, password, full_name, email, address, phone, is_online_customer) VALUES
('U001', 'admin', 'admin123', 'System Administrator', '<EMAIL>', '123 Admin St', '************', FALSE),
('U002', 'customer', 'customer123', 'John Doe', '<EMAIL>', '456 Customer Ave', '************', TRUE),
('U003', 'manager', 'manager123', 'Jane Smith', '<EMAIL>', '789 Manager Blvd', '************', FALSE),
('U004', 'online_user', 'online123', 'Bob Wilson', '<EMAIL>', '321 Online St', '************', TRUE)
ON DUPLICATE KEY UPDATE 
username = VALUES(username), 
password = VALUES(password), 
full_name = VALUES(full_name), 
email = VALUES(email), 
address = VALUES(address), 
phone = VALUES(phone), 
is_online_customer = VALUES(is_online_customer);

-- Insert sample stock batches
-- Shelf stock
INSERT INTO stock_batches (batch_id, item_code, quantity, received_date, expiry_date, stock_type) VALUES
('SBI001', 'I001', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI002', 'I002', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI003', 'I003', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI004', 'I004', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI005', 'I005', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI006', 'I006', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI007', 'I007', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI008', 'I008', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI009', 'I009', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF'),
('SBI010', 'I010', 20, DATE_SUB(CURDATE(), INTERVAL 5 DAY), DATE_ADD(CURDATE(), INTERVAL 6 MONTH), 'SHELF')
ON DUPLICATE KEY UPDATE 
quantity = VALUES(quantity), 
received_date = VALUES(received_date), 
expiry_date = VALUES(expiry_date), 
stock_type = VALUES(stock_type);

-- Storage stock
INSERT INTO stock_batches (batch_id, item_code, quantity, received_date, expiry_date, stock_type) VALUES
('STI001', 'I001', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI002', 'I002', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI003', 'I003', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI004', 'I004', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI005', 'I005', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI006', 'I006', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI007', 'I007', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI008', 'I008', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI009', 'I009', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE'),
('STI010', 'I010', 80, DATE_SUB(CURDATE(), INTERVAL 10 DAY), DATE_ADD(CURDATE(), INTERVAL 8 MONTH), 'STORAGE')
ON DUPLICATE KEY UPDATE 
quantity = VALUES(quantity), 
received_date = VALUES(received_date), 
expiry_date = VALUES(expiry_date), 
stock_type = VALUES(stock_type);

-- Online stock
INSERT INTO stock_batches (batch_id, item_code, quantity, received_date, expiry_date, stock_type) VALUES
('ONI001', 'I001', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI002', 'I002', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI003', 'I003', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI004', 'I004', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI005', 'I005', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI006', 'I006', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI007', 'I007', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI008', 'I008', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI009', 'I009', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE'),
('ONI010', 'I010', 50, DATE_SUB(CURDATE(), INTERVAL 7 DAY), DATE_ADD(CURDATE(), INTERVAL 7 MONTH), 'ONLINE')
ON DUPLICATE KEY UPDATE 
quantity = VALUES(quantity), 
received_date = VALUES(received_date), 
expiry_date = VALUES(expiry_date), 
stock_type = VALUES(stock_type);

-- Show data counts
SELECT 'Items' as Table_Name, COUNT(*) as Record_Count FROM items
UNION ALL
SELECT 'Users' as Table_Name, COUNT(*) as Record_Count FROM users
UNION ALL
SELECT 'Stock Batches' as Table_Name, COUNT(*) as Record_Count FROM stock_batches
UNION ALL
SELECT 'Transactions' as Table_Name, COUNT(*) as Record_Count FROM transactions
UNION ALL
SELECT 'Bills' as Table_Name, COUNT(*) as Record_Count FROM bills;
