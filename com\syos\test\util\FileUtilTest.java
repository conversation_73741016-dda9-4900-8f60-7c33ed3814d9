package com.syos.test.util;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import static org.junit.Assert.*;

import com.syos.util.FileUtil;
import java.io.File;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;

/**
 * Comprehensive unit tests for FileUtil class
 * Tests all file operations and edge cases
 */
public class FileUtilTest {

    private static final String TEST_FILE_PATH = "test_files/test_file.txt";
    private static final String TEST_DIR_PATH = "test_files";
    private static final String NON_EXISTENT_FILE = "test_files/non_existent.txt";

    @Before
    public void setUp() {
        // Create test directory
        FileUtil.ensureDirectoryExists(TEST_DIR_PATH);

        // Clean up any existing test files
        cleanupTestFiles();
    }

    @After
    public void tearDown() {
        cleanupTestFiles();

        // Remove test directory
        File testDir = new File(TEST_DIR_PATH);
        if (testDir.exists()) {
            testDir.delete();
        }
    }

    private void cleanupTestFiles() {
        String[] testFiles = {
                TEST_FILE_PATH,
                "test_files/append_test.txt",
                "test_files/lines_test.txt",
                "test_files/empty_test.txt"
        };

        for (String filePath : testFiles) {
            FileUtil.deleteFile(filePath);
        }
    }

    // ========== FILE EXISTENCE TESTS ==========

    @Test
    public void testFileExists() {
        // Test non-existent file
        assertFalse("Non-existent file should not exist", FileUtil.fileExists(NON_EXISTENT_FILE));

        // Create a file and test existence
        List<String> testLines = Arrays.asList("Test content");
        FileUtil.writeLinesToFile(TEST_FILE_PATH, testLines);
        assertTrue("Created file should exist", FileUtil.fileExists(TEST_FILE_PATH));
    }

    @Test
    public void testFileExistsWithNullPath() {
        try {
            FileUtil.fileExists(null);
            fail("Should throw exception for null path");
        } catch (Exception e) {
            // Expected - NullPointerException or similar
        }
    }

    @Test
    public void testFileExistsWithEmptyPath() {
        assertFalse("Empty path should return false", FileUtil.fileExists(""));
    }

    // ========== DIRECTORY OPERATIONS TESTS ==========

    @Test
    public void testEnsureDirectoryExists() {
        String newDirPath = "test_files/new_directory";

        boolean created = FileUtil.ensureDirectoryExists(newDirPath);
        assertTrue("Directory should be created successfully", created);
        assertTrue("Directory should exist after creation", new File(newDirPath).exists());

        // Clean up
        new File(newDirPath).delete();
    }

    @Test
    public void testEnsureExistingDirectory() {
        // Directory already exists from setUp
        boolean created = FileUtil.ensureDirectoryExists(TEST_DIR_PATH);
        assertTrue("Ensuring existing directory should return true", created);
    }

    @Test
    public void testEnsureDirectoryExistsWithInvalidPath() {
        // Test with a path that contains invalid characters (on Windows)
        String invalidPath = "test_files/invalid<>path";
        boolean created = FileUtil.ensureDirectoryExists(invalidPath);
        assertFalse("Creating directory with invalid path should return false", created);
    }

    // ========== FILE WRITING TESTS ==========

    @Test
    public void testWriteLinesToFile() {
        List<String> lines = Arrays.asList(
                "This is test content",
                "With multiple lines",
                "For testing purposes");

        boolean written = FileUtil.writeLinesToFile(TEST_FILE_PATH, lines);
        assertTrue("File should be written successfully", written);
        assertTrue("File should exist after writing", FileUtil.fileExists(TEST_FILE_PATH));

        List<String> readLines = FileUtil.readLinesFromFile(TEST_FILE_PATH);
        assertEquals("Read lines should match written lines", lines, readLines);
    }

    @Test
    public void testWriteLinesToFileWithNull() {
        boolean written1 = FileUtil.writeLinesToFile(null, Arrays.asList("content"));
        boolean written2 = FileUtil.writeLinesToFile(TEST_FILE_PATH, null);

        assertFalse("Writing to null path should return false", written1);
        assertFalse("Writing null content should return false", written2);
    }

    @Test
    public void testWriteEmptyLines() {
        List<String> emptyLines = Arrays.asList();
        boolean written = FileUtil.writeLinesToFile(TEST_FILE_PATH, emptyLines);
        assertTrue("Writing empty lines should succeed", written);

        List<String> readLines = FileUtil.readLinesFromFile(TEST_FILE_PATH);
        assertEquals("Read lines should be empty", emptyLines, readLines);
    }

    @Test
    public void testWriteLinesWithEmptyStrings() {
        List<String> linesWithEmpty = Arrays.asList("Line 1", "", "Line 3");
        boolean written = FileUtil.writeLinesToFile(TEST_FILE_PATH, linesWithEmpty);
        assertTrue("Writing lines with empty strings should succeed", written);

        List<String> readLines = FileUtil.readLinesFromFile(TEST_FILE_PATH);
        assertEquals("Read lines should match including empty lines", linesWithEmpty, readLines);
    }

    // ========== FILE READING TESTS ==========

    @Test
    public void testReadLinesFromFile() {
        List<String> originalLines = Arrays.asList("Line 1", "Line 2", "Line 3");
        FileUtil.writeLinesToFile(TEST_FILE_PATH, originalLines);

        List<String> readLines = FileUtil.readLinesFromFile(TEST_FILE_PATH);
        assertEquals("Read lines should match original", originalLines, readLines);
    }

    @Test
    public void testReadLinesFromNonExistentFile() {
        List<String> lines = FileUtil.readLinesFromFile(NON_EXISTENT_FILE);
        assertNotNull("Reading non-existent file should return empty list", lines);
        assertTrue("Reading non-existent file should return empty list", lines.isEmpty());
    }

    @Test
    public void testReadLinesFromFileWithNull() {
        List<String> lines = FileUtil.readLinesFromFile(null);
        assertNotNull("Reading from null path should return empty list", lines);
        assertTrue("Reading from null path should return empty list", lines.isEmpty());
    }

    // ========== FILE APPENDING TESTS ==========

    @Test
    public void testAppendLineToFile() {
        String appendTestFile = "test_files/append_test.txt";
        List<String> initialLines = Arrays.asList("Initial line 1", "Initial line 2");
        String appendedLine = "Appended line";

        FileUtil.writeLinesToFile(appendTestFile, initialLines);
        boolean appended = FileUtil.appendLineToFile(appendTestFile, appendedLine);

        assertTrue("Line should be appended successfully", appended);

        List<String> finalLines = FileUtil.readLinesFromFile(appendTestFile);
        assertEquals("Should have 3 lines total", 3, finalLines.size());
        assertEquals("First line should match", "Initial line 1", finalLines.get(0));
        assertEquals("Second line should match", "Initial line 2", finalLines.get(1));
        assertEquals("Appended line should match", appendedLine, finalLines.get(2));
    }

    @Test
    public void testAppendLineToNonExistentFile() {
        String appendTestFile = "test_files/new_append_test.txt";
        String line = "New file line";

        boolean appended = FileUtil.appendLineToFile(appendTestFile, line);
        assertTrue("Appending to non-existent file should create it", appended);

        List<String> readLines = FileUtil.readLinesFromFile(appendTestFile);
        assertEquals("Should have 1 line", 1, readLines.size());
        assertEquals("Line should match", line, readLines.get(0));

        // Clean up
        FileUtil.deleteFile(appendTestFile);
    }

    @Test
    public void testAppendLineWithNull() {
        boolean appended1 = FileUtil.appendLineToFile(null, "content");
        boolean appended2 = FileUtil.appendLineToFile(TEST_FILE_PATH, null);

        assertFalse("Appending to null path should return false", appended1);
        assertFalse("Appending null content should return false", appended2);
    }

    // ========== FILE DELETION TESTS ==========

    @Test
    public void testDeleteFile() {
        // Create a file first
        List<String> lines = Arrays.asList("Test content");
        FileUtil.writeLinesToFile(TEST_FILE_PATH, lines);
        assertTrue("File should exist before deletion", FileUtil.fileExists(TEST_FILE_PATH));

        boolean deleted = FileUtil.deleteFile(TEST_FILE_PATH);
        assertTrue("File should be deleted successfully", deleted);
        assertFalse("File should not exist after deletion", FileUtil.fileExists(TEST_FILE_PATH));
    }

    @Test
    public void testDeleteNonExistentFile() {
        boolean deleted = FileUtil.deleteFile(NON_EXISTENT_FILE);
        assertFalse("Deleting non-existent file should return false", deleted);
    }

    @Test
    public void testDeleteFileWithNull() {
        boolean deleted = FileUtil.deleteFile(null);
        assertFalse("Deleting with null path should return false", deleted);
    }

    // ========== EDGE CASE TESTS ==========

    @Test
    public void testFileOperationsWithSpecialCharacters() {
        String specialFile = "test_files/special_file_with_spaces and symbols.txt";
        List<String> lines = Arrays.asList("Line with special chars: !@#$%^&*()", "Another line");

        boolean written = FileUtil.writeLinesToFile(specialFile, lines);
        assertTrue("Should write file with special characters", written);

        List<String> readLines = FileUtil.readLinesFromFile(specialFile);
        assertEquals("Should read special characters correctly", lines, readLines);

        // Clean up
        FileUtil.deleteFile(specialFile);
    }

    @Test
    public void testLargeFileOperations() {
        List<String> largeContent = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            largeContent.add("Line " + i + " with some content to make it longer");
        }

        boolean written = FileUtil.writeLinesToFile(TEST_FILE_PATH, largeContent);
        assertTrue("Should write large file successfully", written);

        List<String> readLines = FileUtil.readLinesFromFile(TEST_FILE_PATH);
        assertEquals("Should read large file correctly", largeContent.size(), readLines.size());
        assertEquals("First line should match", largeContent.get(0), readLines.get(0));
        assertEquals("Last line should match", largeContent.get(999), readLines.get(999));
    }
}
