package com.syos.test;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;
import org.junit.runners.Suite.SuiteClasses;

import com.syos.test.model.*;
import com.syos.test.service.*;
import com.syos.test.repository.*;
import com.syos.test.util.*;
import com.syos.test.integration.*;

/**
 * Main test suite for SYOS application
 * Runs all unit and integration tests
 */
@RunWith(Suite.class)
@SuiteClasses({
    // Model Tests
    ItemTest.class,
    StockBatchTest.class,
    TransactionTest.class,
    UserTest.class,
    BillTest.class,
    CartItemTest.class,
    
    // Service Tests
    InventoryServiceTest.class,
    BillingServiceTest.class,
    UserServiceTest.class,
    ReportServiceTest.class,
    
    // Repository Tests
    ItemRepositoryTest.class,
    StockRepositoryTest.class,
    TransactionRepositoryTest.class,
    UserRepositoryTest.class,
    BillRepositoryTest.class,
    
    // Utility Tests
    DateUtilTest.class,
    FileUtilTest.class,
    DatabaseUtilTest.class,
    
    // Integration Tests
    BillingWorkflowTest.class,
    InventoryWorkflowTest.class,
    ReportGenerationTest.class
})
public class SyosTestSuite {
    // Test suite class - no implementation needed
    // JUnit will automatically run all specified test classes
}
