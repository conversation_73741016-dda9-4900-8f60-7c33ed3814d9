package com.syos.test;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;
import org.junit.runners.Suite.SuiteClasses;
import org.junit.BeforeClass;
import org.junit.AfterClass;

import com.syos.test.model.*;
import com.syos.test.service.*;
import com.syos.test.repository.*;
import com.syos.test.util.*;
import com.syos.test.integration.*;

/**
 * Comprehensive test suite that runs all SYOS tests
 *
 * This test suite covers:
 * - Unit tests for all model classes
 * - Unit tests for all service classes
 * - Unit tests for all repository classes
 * - Unit tests for all utility classes
 * - Integration tests for complete workflows
 *
 * Total test coverage includes:
 * - 6 Model classes with comprehensive unit tests
 * - 4 Service classes with comprehensive unit tests
 * - 5 Repository classes with comprehensive unit tests
 * - 3 Utility classes with comprehensive unit tests
 * - 3 Integration test workflows
 *
 * Run this suite to execute all tests in the SYOS system.
 */
@RunWith(Suite.class)
@SuiteClasses({
        // Model Tests
        ItemTest.class,
        StockBatchTest.class,
        TransactionTest.class,
        UserTest.class,
        BillTest.class,
        CartItemTest.class,

        // Service Tests
        InventoryServiceTest.class,
        BillingServiceTest.class,
        UserServiceTest.class,
        ReportServiceTest.class,

        // Repository Tests
        ItemRepositoryTest.class,
        StockRepositoryTest.class,
        TransactionRepositoryTest.class,
        UserRepositoryTest.class,
        BillRepositoryTest.class,

        // Utility Tests
        DateUtilTest.class,
        FileUtilTest.class,
        DatabaseUtilTest.class,

        // Integration Tests
        BillingWorkflowTest.class,
        InventoryWorkflowTest.class,
        ReportGenerationTest.class
})
public class SyosTestSuite {

    @BeforeClass
    public static void setUpSuite() {
        System.out.println("=".repeat(80));
        System.out.println("STARTING SYOS COMPREHENSIVE TEST SUITE");
        System.out.println("=".repeat(80));
        System.out.println("Running comprehensive tests for Synex Outlet Store (SYOS) system");
        System.out.println("Test categories: Models, Services, Repositories, Utilities, Integration");
        System.out.println("Total test classes: 21");
        System.out.println("=".repeat(80));
    }

    @AfterClass
    public static void tearDownSuite() {
        System.out.println("=".repeat(80));
        System.out.println("SYOS COMPREHENSIVE TEST SUITE COMPLETED");
        System.out.println("=".repeat(80));
        System.out.println("All tests have been executed.");
        System.out.println("Check individual test results for detailed information.");
        System.out.println("=".repeat(80));
    }
}
