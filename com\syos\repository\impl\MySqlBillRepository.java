package com.syos.repository.impl;

import com.syos.model.*;
import com.syos.repository.BillRepository;
import com.syos.util.DatabaseUtil;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MySQL implementation of BillRepository.
 */
public class MySqlBillRepository implements BillRepository {

    @Override
    public Bill save(Bill bill) {
        String sql = """
                    INSERT INTO bills (bill_number, transaction_id, generated_date_time)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    transaction_id = VALUES(transaction_id),
                    generated_date_time = VALUES(generated_date_time)
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, bill.getBillNumber());
            stmt.setString(2, bill.getTransaction().getTransactionId());
            stmt.setTimestamp(3, Timestamp.valueOf(bill.getGeneratedDateTime()));

            stmt.executeUpdate();
            return bill;

        } catch (SQLException e) {
            System.err.println("Error saving bill: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Optional<Bill> findByNumber(String billNumber) {
        return findByBillNumber(billNumber);
    }

    @Override
    public List<Bill> findByTransactionId(String transactionId) {
        List<Bill> bills = new ArrayList<>();
        String sql = """
                    SELECT b.*, t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM bills b
                    JOIN transactions t ON b.transaction_id = t.transaction_id
                    LEFT JOIN users u ON t.user_id = u.user_id
                    WHERE b.transaction_id = ?
                    ORDER BY b.generated_date_time DESC
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, transactionId);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                bills.add(createBillFromResultSet(rs));
            }

        } catch (SQLException e) {
            System.err.println("Error finding bills by transaction ID: " + e.getMessage());
            e.printStackTrace();
        }

        return bills;
    }

    public Optional<Bill> findByBillNumber(String billNumber) {
        String sql = """
                    SELECT b.*, t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM bills b
                    JOIN transactions t ON b.transaction_id = t.transaction_id
                    LEFT JOIN users u ON t.user_id = u.user_id
                    WHERE b.bill_number = ?
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, billNumber);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                Bill bill = createBillFromResultSet(rs);
                return Optional.of(bill);
            }

        } catch (SQLException e) {
            System.err.println("Error finding bill by number: " + e.getMessage());
            e.printStackTrace();
        }

        return Optional.empty();
    }

    @Override
    public List<Bill> findByDate(LocalDate date) {
        List<Bill> bills = new ArrayList<>();
        String sql = """
                    SELECT b.*, t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM bills b
                    JOIN transactions t ON b.transaction_id = t.transaction_id
                    LEFT JOIN users u ON t.user_id = u.user_id
                    WHERE DATE(b.generated_date_time) = ?
                    ORDER BY b.generated_date_time DESC
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setDate(1, Date.valueOf(date));
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                bills.add(createBillFromResultSet(rs));
            }

        } catch (SQLException e) {
            System.err.println("Error finding bills by date: " + e.getMessage());
            e.printStackTrace();
        }

        return bills;
    }

    @Override
    public List<Bill> findAll() {
        List<Bill> bills = new ArrayList<>();
        String sql = """
                    SELECT b.*, t.*, u.username, u.password, u.full_name, u.email, u.address, u.phone, u.is_online_customer
                    FROM bills b
                    JOIN transactions t ON b.transaction_id = t.transaction_id
                    LEFT JOIN users u ON t.user_id = u.user_id
                    ORDER BY b.generated_date_time DESC
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                bills.add(createBillFromResultSet(rs));
            }

        } catch (SQLException e) {
            System.err.println("Error finding all bills: " + e.getMessage());
            e.printStackTrace();
        }

        return bills;
    }

    @Override
    public boolean delete(String billNumber) {
        String sql = "DELETE FROM bills WHERE bill_number = ?";

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, billNumber);
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;

        } catch (SQLException e) {
            System.err.println("Error deleting bill: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public String getNextBillNumber() {
        String sql = "SELECT MAX(CAST(SUBSTRING(bill_number, 2) AS UNSIGNED)) FROM bills WHERE bill_number LIKE 'B%'";

        try (Connection conn = DatabaseUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                int maxNumber = rs.getInt(1);
                return String.format("B%03d", maxNumber + 1);
            }

        } catch (SQLException e) {
            System.err.println("Error getting next bill number: " + e.getMessage());
            e.printStackTrace();
        }

        return "B001"; // Default first bill number
    }

    @Override
    public long count() {
        String sql = "SELECT COUNT(*) FROM bills";

        try (Connection conn = DatabaseUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getLong(1);
            }

        } catch (SQLException e) {
            System.err.println("Error counting bills: " + e.getMessage());
            e.printStackTrace();
        }

        return 0;
    }

    /**
     * Creates a Bill object from a ResultSet.
     */
    private Bill createBillFromResultSet(ResultSet rs) throws SQLException {
        // Create user if exists
        User user = null;
        if (rs.getString("user_id") != null) {
            user = new User(
                    rs.getString("user_id"),
                    rs.getString("username"),
                    rs.getString("password"),
                    rs.getString("full_name"),
                    rs.getString("email"),
                    rs.getString("address"),
                    rs.getString("phone"),
                    rs.getBoolean("is_online_customer"));
        }

        // Create transaction
        Transaction transaction = new Transaction(
                rs.getString("transaction_id"),
                user,
                TransactionType.valueOf(rs.getString("transaction_type")));

        // Set transaction details
        Timestamp transactionTimestamp = rs.getTimestamp("transaction_date");
        if (transactionTimestamp != null) {
            transaction.setDateTime(transactionTimestamp.toLocalDateTime());
        }

        transaction.setTotalAmount(rs.getDouble("total_amount"));
        transaction.setDiscountAmount(rs.getDouble("discount_amount"));
        transaction.setFinalAmount(rs.getDouble("final_amount"));
        transaction.setCashTendered(rs.getDouble("cash_tendered"));
        transaction.setChange(rs.getDouble("change_amount"));

        // Load transaction items
        loadTransactionItems(transaction);

        // Create bill
        Bill bill = new Bill(rs.getString("bill_number"), transaction);

        // Set bill generation time
        Timestamp billTimestamp = rs.getTimestamp("generated_date_time");
        if (billTimestamp != null) {
            bill.setGeneratedDateTime(billTimestamp.toLocalDateTime());
        }

        return bill;
    }

    /**
     * Loads transaction items for a transaction.
     */
    private void loadTransactionItems(Transaction transaction) throws SQLException {
        String sql = """
                    SELECT ti.*, i.name, i.category
                    FROM transaction_items ti
                    JOIN items i ON ti.item_code = i.code
                    WHERE ti.transaction_id = ?
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, transaction.getTransactionId());
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                Item item = new Item(
                        rs.getString("item_code"),
                        rs.getString("name"),
                        rs.getDouble("unit_price"),
                        rs.getString("category"));

                transaction.addItem(item, rs.getInt("quantity"));
            }
        }
    }
}
