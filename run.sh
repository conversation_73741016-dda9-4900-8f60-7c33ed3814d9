#!/bin/bash

echo "Starting SYOS with MySQL Database..."

# Create necessary directories
mkdir -p bin reports

echo "Compiling application..."
javac -d bin -cp "lib/mysql-connector-java-5.1.49.jar" com/syos/config/*.java com/syos/util/*.java com/syos/model/*.java com/syos/repository/*.java com/syos/repository/impl/*.java com/syos/factory/*.java com/syos/service/*.java com/syos/ui/*.java com/syos/*.java

if [ $? -ne 0 ]; then
    echo "Compilation failed!"
    exit 1
fi

echo "Running SYOS..."
java -cp "bin:lib/mysql-connector-java-5.1.49.jar" com.syos.SyosApplication
