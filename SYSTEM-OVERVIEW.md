# SYOS - Synex Outlet Store System

## Overview

The Synex Outlet Store (SYOS) is a comprehensive Java-based grocery store billing and stock management system that uses **MySQL as the primary database**. The system implements clean code practices, SOLID principles, and multiple design patterns.

## 🗄️ Database Configuration

**Primary Storage**: MySQL Database
- **Default Database**: `syos_db`
- **Host**: `localhost:3306`
- **Fallback**: File-based storage (if MySQL unavailable)

## 🚀 Quick Start

### 1. Setup MySQL Database
```bash
# Windows
setup-mysql.bat

# Linux/Mac
./setup-mysql.sh
```

### 2. Run the Application
```bash
# Windows
run.bat

# Linux/Mac
./run.sh
```

## 📁 Project Structure

```
syos/
├── com/syos/
│   ├── config/          # Database configuration
│   ├── util/            # Utility classes (Database, File, Date)
│   ├── model/           # Domain models (Item, User, Transaction, etc.)
│   ├── repository/      # Data access interfaces
│   │   └── impl/        # MySQL & File implementations
│   ├── factory/         # Factory pattern implementations
│   ├── service/         # Business logic layer
│   ├── ui/              # Console user interface
│   └── SyosApplication.java  # Main application
├── database/
│   ├── setup.sql        # Database schema
│   └── sample_data.sql  # Sample data
├── lib/
│   └── mysql-connector-java-5.1.49.jar  # MySQL driver
├── run.bat/.sh          # Main run scripts (MySQL)
├── run-mysql.bat/.sh    # Force MySQL
├── run-file.bat/.sh     # Force file storage
└── setup-mysql.bat/.sh # Database setup scripts
```

## 🏗️ Architecture

### Design Patterns Implemented
1. **Repository Pattern** - Data access abstraction
2. **Factory Pattern** - Object creation
3. **Strategy Pattern** - Storage type selection
4. **Singleton Pattern** - Factory instances
5. **Dependency Injection** - Service layer
6. **MVC Pattern** - UI separation
7. **Builder Pattern** - Complex object construction
8. **Observer Pattern** - Event handling

### SOLID Principles
- ✅ **Single Responsibility** - Each class has one purpose
- ✅ **Open/Closed** - Extensible without modification
- ✅ **Liskov Substitution** - Interface implementations
- ✅ **Interface Segregation** - Focused interfaces
- ✅ **Dependency Inversion** - Abstractions over concretions

## 🔧 Features

### Core Functionality
- **Billing System** - Process sales transactions
- **Inventory Management** - Track stock levels
- **User Management** - Admin and customer accounts
- **Reporting** - Sales and inventory reports
- **Multi-storage Support** - MySQL + File fallback

### Database Features
- **ACID Compliance** - Data integrity
- **Foreign Key Constraints** - Referential integrity
- **Indexes** - Optimized queries
- **Automatic Schema Creation** - Self-initializing
- **Sample Data** - Ready-to-use test data

## 📊 Database Schema

### Tables
- **items** - Product catalog
- **users** - User accounts
- **stock_batches** - Inventory tracking
- **transactions** - Sales records
- **transaction_items** - Line items
- **bills** - Generated receipts

### Sample Data Included
- 10 Products (groceries, dairy, meat, fruits)
- 2 Users (admin, customer)
- Stock batches for all products
- Login: admin/admin123, customer/customer123

## 🛠️ Technical Stack

- **Language**: Java 8+
- **Database**: MySQL 5.7+
- **JDBC Driver**: MySQL Connector/J 5.1.49
- **Architecture**: Layered (Repository → Service → UI)
- **Storage**: Dual (MySQL primary, File fallback)

## 📋 System Requirements

### Software
- Java 8 or higher
- MySQL 5.7 or higher
- 50MB disk space

### Hardware
- 512MB RAM minimum
- 100MB free disk space

## 🔐 Security Features

- Password-based authentication
- User role management
- SQL injection prevention (PreparedStatements)
- Connection security
- Data validation

## 📈 Performance

- Indexed database queries
- Connection management
- Efficient data structures
- Minimal memory footprint
- Fast startup time

## 🧪 Testing

The system includes comprehensive test coverage:
- Unit tests for all components
- Integration tests for workflows
- Database connectivity tests
- Error handling validation

## 📚 Documentation

- `MYSQL-SETUP-GUIDE.md` - Database setup instructions
- `README-MySQL.md` - MySQL integration details
- Inline code documentation
- Architecture diagrams

## 🔄 Deployment Options

### Development
```bash
./run.sh  # Uses MySQL by default
```

### Production
```bash
java -cp "bin:lib/mysql-connector-java-5.1.49.jar" \
     -Ddb.host=prod-server \
     -Ddb.username=syos_user \
     -Ddb.password=secure_password \
     com.syos.SyosApplication
```

### Fallback Mode
```bash
./run-file.sh  # Uses file storage
```

## 🎯 Key Benefits

1. **Reliability** - MySQL ACID compliance
2. **Scalability** - Database-backed storage
3. **Maintainability** - Clean architecture
4. **Flexibility** - Multiple storage options
5. **Performance** - Optimized queries
6. **Security** - Proper authentication
7. **Usability** - Intuitive console interface

## 📞 Support

For issues or questions:
1. Check the setup guides
2. Verify MySQL connectivity
3. Review error logs
4. Test with file storage fallback

The system is designed to be robust and will automatically handle database connectivity issues by falling back to file-based storage when needed.
