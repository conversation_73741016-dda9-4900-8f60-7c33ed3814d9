@echo off

echo Starting SYOS with MySQL Database...

REM Create necessary directories
if not exist bin mkdir bin
if not exist reports mkdir reports

echo Compiling application...
javac -d bin -cp "lib/mysql-connector-java-5.1.49.jar" com/syos/config/*.java com/syos/util/*.java com/syos/model/*.java com/syos/repository/*.java com/syos/repository/impl/*.java com/syos/factory/*.java com/syos/service/*.java com/syos/ui/*.java com/syos/*.java

if %errorlevel% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Running SYOS...
java -cp "bin;lib/mysql-connector-java-5.1.49.jar" com.syos.SyosApplication

pause
