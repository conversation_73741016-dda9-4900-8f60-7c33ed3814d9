# SYOS MySQL Database Setup Guide

This guide will help you set up MySQL as the database for the Synex Outlet Store (SYOS) system.

## Prerequisites

### 1. Install MySQL

#### Windows:
1. Download MySQL Community Server from: https://dev.mysql.com/downloads/mysql/
2. Run the installer and follow the setup wizard
3. Set a root password during installation (remember this password!)
4. Add MySQL to your system PATH

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

#### Linux (CentOS/RHEL):
```bash
sudo yum install mysql-server
sudo systemctl start mysqld
sudo mysql_secure_installation
```

#### macOS:
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

### 2. Verify MySQL Installation

Open a terminal/command prompt and run:
```bash
mysql --version
```

You should see output like: `mysql Ver 8.0.x`

## Quick Setup (Automated)

### Windows:
```batch
setup-mysql.bat
```

### Linux/Mac:
```bash
chmod +x setup-mysql.sh
./setup-mysql.sh
```

## Manual Setup

### 1. Create Database

Connect to MySQL as root:
```bash
mysql -u root -p
```

Run the setup script:
```sql
source database/setup.sql;
```

### 2. Load Sample Data (Optional)

```sql
source database/sample_data.sql;
```

### 3. Exit MySQL
```sql
exit;
```

## Running the Application

### Default (MySQL):
```bash
# Windows
run.bat

# Linux/Mac
./run.sh
```

### Force MySQL:
```bash
# Windows
run-mysql.bat

# Linux/Mac
./run-mysql.sh
```

### Force File Storage:
```bash
# Windows
run-file.bat

# Linux/Mac
./run-file.sh
```

## Database Configuration

The system uses these default settings:
- **Host**: localhost
- **Port**: 3306
- **Database**: syos_db
- **Username**: root
- **Password**: (your MySQL root password)

### Custom Configuration

You can override these settings using system properties:

```bash
java -cp "bin:lib/mysql-connector-java-5.1.49.jar" \
     -Ddb.host=your-host \
     -Ddb.port=3306 \
     -Ddb.name=your-database \
     -Ddb.username=your-username \
     -Ddb.password=your-password \
     com.syos.SyosApplication
```

## Database Schema

The system creates these tables:
- **items** - Product catalog
- **users** - User accounts
- **stock_batches** - Inventory management
- **transactions** - Sales transactions
- **transaction_items** - Transaction line items
- **bills** - Generated bills

## Troubleshooting

### Connection Issues

1. **"Access denied for user 'root'"**
   - Check your MySQL root password
   - Reset password if needed: `ALTER USER 'root'@'localhost' IDENTIFIED BY 'newpassword';`

2. **"Can't connect to MySQL server"**
   - Ensure MySQL service is running
   - Check if MySQL is listening on port 3306: `netstat -an | grep 3306`

3. **"Unknown database 'syos_db'"**
   - Run the setup script: `mysql -u root -p < database/setup.sql`

### Performance Issues

1. **Slow queries**
   - The system includes indexes for common queries
   - Monitor with: `SHOW PROCESSLIST;`

2. **Connection timeouts**
   - Increase MySQL timeout settings in my.cnf

### Security Recommendations

1. **Create dedicated user** (instead of using root):
```sql
CREATE USER 'syos_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON syos_db.* TO 'syos_user'@'localhost';
FLUSH PRIVILEGES;
```

2. **Update connection settings**:
```bash
java -Ddb.username=syos_user -Ddb.password=strong_password ...
```

## Backup and Restore

### Backup:
```bash
mysqldump -u root -p syos_db > syos_backup.sql
```

### Restore:
```bash
mysql -u root -p syos_db < syos_backup.sql
```

## Sample Data

The system includes sample data with:
- 10 sample products (groceries, dairy, meat, fruits)
- 2 sample users (admin and customer)
- Stock batches for all products (shelf, storage, online)

Login credentials:
- **Admin**: username=`admin`, password=`admin123`
- **Customer**: username=`customer`, password=`customer123`

## Support

If you encounter issues:
1. Check MySQL error logs
2. Verify database connectivity
3. Ensure all tables are created properly
4. Check the application logs for detailed error messages

The system will automatically fall back to file-based storage if MySQL is unavailable.
