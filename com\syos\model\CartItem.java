package com.syos.model;

/**
 * Represents an item in a shopping cart.
 * Contains the item and the quantity selected.
 */
public class CartItem {
    private Item item;
    private int quantity;

    public CartItem() {
    }

    public CartItem(Item item, int quantity) {
        if (item == null) {
            throw new IllegalArgumentException("Item cannot be null");
        }
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity must be positive");
        }
        this.item = item;
        this.quantity = quantity;
    }

    // Constructor for test compatibility (itemCode, quantity, unitPrice)
    public CartItem(String itemCode, int quantity, double unitPrice) {
        if (itemCode == null || itemCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Item code cannot be null or empty");
        }
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity must be positive");
        }
        if (unitPrice < 0) {
            throw new IllegalArgumentException("Unit price cannot be negative");
        }

        // Create a temporary item for testing purposes
        this.item = new Item(itemCode, "Test Item", unitPrice, "Test Category");
        this.quantity = quantity;
    }

    // Getters and Setters
    public Item getItem() {
        return item;
    }

    public void setItem(Item item) {
        this.item = item;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    /**
     * Calculates the subtotal for this cart item.
     * 
     * @return The subtotal (price * quantity)
     */
    public double getSubtotal() {
        return item.getPrice() * quantity;
    }

    @Override
    public String toString() {
        return "CartItem{" +
                "item=" + item +
                ", quantity=" + quantity +
                ", subtotal=" + getSubtotal() +
                '}';
    }

    // Additional methods for test compatibility
    public String getItemCode() {
        return item != null ? item.getCode() : null;
    }

    public double getUnitPrice() {
        return item != null ? item.getPrice() : 0.0;
    }
}
