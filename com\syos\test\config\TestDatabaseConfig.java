package com.syos.test.config;

import com.syos.config.DatabaseConfig;

/**
 * Test database configuration that uses a separate test database.
 */
public class TestDatabaseConfig extends DatabaseConfig {
    
    public static final String TEST_DATABASE = "syos_test_db";
    
    /**
     * Gets the test database name.
     * 
     * @return The test database name
     */
    public static String getTestDatabaseName() {
        return System.getProperty(DB_NAME_PROPERTY, TEST_DATABASE);
    }
    
    /**
     * Gets the test JDBC URL.
     * 
     * @return The test JDBC URL
     */
    public static String getTestJdbcUrl() {
        return String.format("**********************************************************************************",
                getHost(), getPort(), getTestDatabaseName());
    }
}
