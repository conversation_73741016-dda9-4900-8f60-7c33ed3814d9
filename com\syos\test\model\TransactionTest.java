package com.syos.test.model;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.syos.model.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Unit tests for Transaction model class
 */
public class TransactionTest {
    
    private Transaction transaction;
    private List<CartItem> cartItems;
    
    @Before
    public void setUp() {
        cartItems = new ArrayList<>();
        cartItems.add(new CartItem("I001", 2, 2.50));
        cartItems.add(new CartItem("I002", 1, 3.00));
        
        transaction = new Transaction("T001", TransactionType.IN_STORE, cartItems, 8.00, 0.0);
    }
    
    @Test
    public void testTransactionCreation() {
        assertNotNull("Transaction should not be null", transaction);
        assertEquals("Transaction ID should match", "T001", transaction.getId());
        assertEquals("Transaction type should match", TransactionType.IN_STORE, transaction.getType());
        assertEquals("Total amount should match", 8.00, transaction.getTotalAmount(), 0.01);
        assertEquals("Discount should match", 0.0, transaction.getDiscountPercentage(), 0.01);
        assertEquals("Cart items size should match", 2, transaction.getCartItems().size());
    }
    
    @Test
    public void testOnlineTransactionWithUser() {
        Transaction onlineTransaction = new Transaction("T002", TransactionType.ONLINE, cartItems, 8.00, 5.0);
        onlineTransaction.setUserId("U001");
        
        assertEquals("Transaction type should be ONLINE", TransactionType.ONLINE, onlineTransaction.getType());
        assertEquals("User ID should be set", "U001", onlineTransaction.getUserId());
        assertEquals("Discount should be applied", 5.0, onlineTransaction.getDiscountPercentage(), 0.01);
    }
    
    @Test
    public void testTransactionTimestamp() {
        LocalDateTime beforeCreation = LocalDateTime.now().minusSeconds(1);
        Transaction newTransaction = new Transaction("T003", TransactionType.IN_STORE, cartItems, 8.00, 0.0);
        LocalDateTime afterCreation = LocalDateTime.now().plusSeconds(1);
        
        assertNotNull("Timestamp should be set", newTransaction.getTimestamp());
        assertTrue("Timestamp should be after before time", 
            newTransaction.getTimestamp().isAfter(beforeCreation));
        assertTrue("Timestamp should be before after time", 
            newTransaction.getTimestamp().isBefore(afterCreation));
    }
    
    @Test
    public void testCalculateDiscountedAmount() {
        Transaction discountedTransaction = new Transaction("T004", TransactionType.IN_STORE, cartItems, 10.00, 20.0);
        
        double discountedAmount = discountedTransaction.getDiscountedAmount();
        assertEquals("Discounted amount should be correct", 8.00, discountedAmount, 0.01);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidTransactionId() {
        new Transaction(null, TransactionType.IN_STORE, cartItems, 8.00, 0.0);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testNegativeAmount() {
        new Transaction("T005", TransactionType.IN_STORE, cartItems, -1.0, 0.0);
    }
    
    @Test(expected = IllegalArgumentException.class)
    public void testInvalidDiscount() {
        new Transaction("T006", TransactionType.IN_STORE, cartItems, 8.00, 150.0); // > 100%
    }
}
