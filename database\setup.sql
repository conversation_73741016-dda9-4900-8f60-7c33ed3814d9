-- SYOS Database Setup Script
-- This script creates the database and user for the Synex Outlet Store system

-- Create database
CREATE DATABASE IF NOT EXISTS syos_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional - you can use existing MySQL user)
-- CREATE USER IF NOT EXISTS 'syos_user'@'localhost' IDENTIFIED BY 'syos_password';
-- GRANT ALL PRIVILEGES ON syos_db.* TO 'syos_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Use the database
USE syos_db;

-- Create items table
CREATE TABLE IF NOT EXISTS items (
    code VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    user_id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    is_online_customer BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create stock_batches table
CREATE TABLE IF NOT EXISTS stock_batches (
    batch_id VARCHAR(50) PRIMARY KEY,
    item_code VARCHAR(50) NOT NULL,
    quantity INT NOT NULL DEFAULT 0,
    received_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    stock_type ENUM('SHELF', 'STORAGE', 'ONLINE') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_code) REFERENCES items(code) ON DELETE CASCADE
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    transaction_id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50),
    transaction_type ENUM('IN_STORE', 'ONLINE') NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    final_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    cash_tendered DECIMAL(10, 2) DEFAULT 0.00,
    change_amount DECIMAL(10, 2) DEFAULT 0.00,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Create transaction_items table (for cart items)
CREATE TABLE IF NOT EXISTS transaction_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) NOT NULL,
    item_code VARCHAR(50) NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    subtotal DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE CASCADE,
    FOREIGN KEY (item_code) REFERENCES items(code) ON DELETE CASCADE
);

-- Create bills table
CREATE TABLE IF NOT EXISTS bills (
    bill_number VARCHAR(50) PRIMARY KEY,
    transaction_id VARCHAR(50) NOT NULL,
    generated_date_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_stock_batches_item_code ON stock_batches(item_code);
CREATE INDEX idx_stock_batches_stock_type ON stock_batches(stock_type);
CREATE INDEX idx_stock_batches_expiry_date ON stock_batches(expiry_date);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transaction_items_transaction_id ON transaction_items(transaction_id);
CREATE INDEX idx_bills_transaction_id ON bills(transaction_id);
CREATE INDEX idx_bills_date ON bills(generated_date_time);

SHOW TABLES;
