package com.syos.repository.impl;

import com.syos.model.Item;
import com.syos.repository.ItemRepository;
import com.syos.util.DatabaseUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MySQL implementation of ItemRepository.
 */
public class MySqlItemRepository implements ItemRepository {

    @Override
    public Item save(Item item) {
        String sql = """
                    INSERT INTO items (code, name, price, category)
                    VALUES (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    price = VALUES(price),
                    category = VALUES(category)
                """;

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, item.getCode());
            stmt.setString(2, item.getName());
            stmt.setDouble(3, item.getPrice());
            stmt.setString(4, item.getCategory());

            stmt.executeUpdate();
            return item;

        } catch (SQLException e) {
            System.err.println("Error saving item: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public Optional<Item> findByCode(String code) {
        String sql = "SELECT * FROM items WHERE code = ?";

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, code);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                Item item = new Item(
                        rs.getString("code"),
                        rs.getString("name"),
                        rs.getDouble("price"),
                        rs.getString("category"));
                return Optional.of(item);
            }

        } catch (SQLException e) {
            System.err.println("Error finding item by code: " + e.getMessage());
            e.printStackTrace();
        }

        return Optional.empty();
    }

    @Override
    public List<Item> findAll() {
        List<Item> items = new ArrayList<>();
        String sql = "SELECT * FROM items ORDER BY code";

        try (Connection conn = DatabaseUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                Item item = new Item(
                        rs.getString("code"),
                        rs.getString("name"),
                        rs.getDouble("price"),
                        rs.getString("category"));
                items.add(item);
            }

        } catch (SQLException e) {
            System.err.println("Error finding all items: " + e.getMessage());
            e.printStackTrace();
        }

        return items;
    }

    @Override
    public Item update(Item item) {
        // For MySQL, save and update are the same due to ON DUPLICATE KEY UPDATE
        return save(item);
    }

    @Override
    public boolean delete(String code) {
        String sql = "DELETE FROM items WHERE code = ?";

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, code);
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;

        } catch (SQLException e) {
            System.err.println("Error deleting item: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public boolean exists(String code) {
        String sql = "SELECT 1 FROM items WHERE code = ?";

        try (Connection conn = DatabaseUtil.getConnection();
                PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, code);
            ResultSet rs = stmt.executeQuery();
            return rs.next();

        } catch (SQLException e) {
            System.err.println("Error checking if item exists: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public long count() {
        String sql = "SELECT COUNT(*) FROM items";

        try (Connection conn = DatabaseUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getLong(1);
            }

        } catch (SQLException e) {
            System.err.println("Error counting items: " + e.getMessage());
            e.printStackTrace();
        }

        return 0;
    }
}
