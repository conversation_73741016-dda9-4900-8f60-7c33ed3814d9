package com.syos.factory;

import com.syos.repository.*;
import com.syos.repository.impl.*;

/**
 * Factory class for creating MySQL repository instances.
 */
public class MySqlRepositoryFactory implements RepositoryFactory {
    
    private static MySqlRepositoryFactory instance;
    
    // Repository instances (singleton pattern)
    private ItemRepository itemRepository;
    private StockRepository stockRepository;
    private UserRepository userRepository;
    private TransactionRepository transactionRepository;
    private BillRepository billRepository;
    
    private MySqlRepositoryFactory() {
        // Private constructor for singleton
    }
    
    /**
     * Gets the singleton instance of the factory.
     * 
     * @return The factory instance
     */
    public static MySqlRepositoryFactory getInstance() {
        if (instance == null) {
            instance = new MySqlRepositoryFactory();
        }
        return instance;
    }
    
    @Override
    public ItemRepository getItemRepository() {
        if (itemRepository == null) {
            itemRepository = new MySqlItemRepository();
        }
        return itemRepository;
    }
    
    @Override
    public StockRepository getStockRepository() {
        if (stockRepository == null) {
            stockRepository = new MySqlStockRepository();
        }
        return stockRepository;
    }
    
    @Override
    public UserRepository getUserRepository() {
        if (userRepository == null) {
            userRepository = new MySqlUserRepository();
        }
        return userRepository;
    }
    
    @Override
    public TransactionRepository getTransactionRepository() {
        if (transactionRepository == null) {
            transactionRepository = new MySqlTransactionRepository();
        }
        return transactionRepository;
    }
    
    @Override
    public BillRepository getBillRepository() {
        if (billRepository == null) {
            billRepository = new MySqlBillRepository();
        }
        return billRepository;
    }
}
