package com.syos.factory;

import com.syos.repository.*;
import com.syos.repository.impl.*;

/**
 * Factory class for creating file-based repository instances.
 */
public class FileRepositoryFactory implements RepositoryFactory {
    
    private static FileRepositoryFactory instance;
    
    // Repository instances (singleton pattern)
    private ItemRepository itemRepository;
    private StockRepository stockRepository;
    private UserRepository userRepository;
    private TransactionRepository transactionRepository;
    private BillRepository billRepository;
    
    private FileRepositoryFactory() {
        // Private constructor for singleton
    }
    
    /**
     * Gets the singleton instance of the factory.
     * 
     * @return The factory instance
     */
    public static FileRepositoryFactory getInstance() {
        if (instance == null) {
            instance = new FileRepositoryFactory();
        }
        return instance;
    }
    
    @Override
    public ItemRepository getItemRepository() {
        if (itemRepository == null) {
            itemRepository = new FileItemRepository();
        }
        return itemRepository;
    }
    
    @Override
    public StockRepository getStockRepository() {
        if (stockRepository == null) {
            stockRepository = new FileStockRepository();
        }
        return stockRepository;
    }
    
    @Override
    public UserRepository getUserRepository() {
        if (userRepository == null) {
            userRepository = new FileUserRepository();
        }
        return userRepository;
    }
    
    @Override
    public TransactionRepository getTransactionRepository() {
        if (transactionRepository == null) {
            transactionRepository = new FileTransactionRepository();
        }
        return transactionRepository;
    }
    
    @Override
    public BillRepository getBillRepository() {
        if (billRepository == null) {
            billRepository = new FileBillRepository();
        }
        return billRepository;
    }
}
