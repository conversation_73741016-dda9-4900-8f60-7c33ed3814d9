@echo off
echo ========================================
echo SYOS MySQL Setup Verification
echo ========================================
echo.

set ERROR_COUNT=0

REM Check 1: MySQL Installation
echo [1/7] Checking MySQL installation...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo   ❌ FAIL: MySQL not found in PATH
    set /a ERROR_COUNT+=1
) else (
    echo   ✅ PASS: MySQL is installed
)
echo.

REM Check 2: .env file
echo [2/7] Checking .env configuration file...
if exist ".env" (
    echo   ✅ PASS: .env file exists
    echo   📄 Contents:
    type .env | findstr "DB_"
) else (
    echo   ❌ FAIL: .env file not found
    echo   💡 Run quick-mysql-setup.bat to create it
    set /a ERROR_COUNT+=1
)
echo.

REM Check 3: JDBC Driver
echo [3/7] Checking MySQL JDBC driver...
if exist "lib\mysql-connector-java-8.0.33.jar" (
    echo   ✅ PASS: MySQL JDBC driver found
) else if exist "lib\mysql-connector-java-5.1.49.jar" (
    echo   ✅ PASS: MySQL JDBC driver found (older version)
) else (
    echo   ❌ FAIL: MySQL JDBC driver not found in lib/
    set /a ERROR_COUNT+=1
)
echo.

REM Check 4: Database Scripts
echo [4/7] Checking database setup scripts...
if exist "database\setup.sql" (
    echo   ✅ PASS: Database setup script found
) else (
    echo   ❌ FAIL: database\setup.sql not found
    set /a ERROR_COUNT+=1
)

if exist "database\sample_data.sql" (
    echo   ✅ PASS: Sample data script found
) else (
    echo   ⚠️  WARN: database\sample_data.sql not found (optional)
)
echo.

REM Check 5: MySQL Service
echo [5/7] Checking MySQL service status...
net start | findstr -i mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo   ❌ FAIL: MySQL service not running
    echo   💡 Try: net start mysql80
    set /a ERROR_COUNT+=1
) else (
    echo   ✅ PASS: MySQL service is running
)
echo.

REM Check 6: Database Connection (if .env exists)
echo [6/7] Testing database connection...
if exist ".env" (
    REM Extract credentials from .env
    for /f "tokens=2 delims==" %%a in ('findstr "DB_USERNAME" .env') do set DB_USER=%%a
    for /f "tokens=2 delims==" %%a in ('findstr "DB_PASSWORD" .env') do set DB_PASS=%%a
    
    if defined DB_USER if defined DB_PASS (
        mysql -u %DB_USER% -p%DB_PASS% -e "SELECT 1;" >nul 2>&1
        if %errorlevel% neq 0 (
            echo   ❌ FAIL: Cannot connect to MySQL with .env credentials
            echo   💡 Check username/password in .env file
            set /a ERROR_COUNT+=1
        ) else (
            echo   ✅ PASS: Database connection successful
        )
    ) else (
        echo   ⚠️  WARN: Could not read credentials from .env file
    )
) else (
    echo   ⚠️  SKIP: No .env file to test connection
)
echo.

REM Check 7: Database and Tables
echo [7/7] Checking database and tables...
if defined DB_USER if defined DB_PASS (
    mysql -u %DB_USER% -p%DB_PASS% -e "USE syos_db; SHOW TABLES;" >nul 2>&1
    if %errorlevel% neq 0 (
        echo   ❌ FAIL: Database 'syos_db' or tables not found
        echo   💡 Run: mysql -u %DB_USER% -p%DB_PASS% syos_db < database\setup.sql
        set /a ERROR_COUNT+=1
    ) else (
        echo   ✅ PASS: Database and tables exist
        echo   📊 Tables found:
        mysql -u %DB_USER% -p%DB_PASS% syos_db -e "SHOW TABLES;" 2>nul | findstr -v "Tables_in"
    )
) else (
    echo   ⚠️  SKIP: Cannot check database without credentials
)
echo.

REM Summary
echo ========================================
echo VERIFICATION SUMMARY
echo ========================================
if %ERROR_COUNT% equ 0 (
    echo ✅ ALL CHECKS PASSED!
    echo Your MySQL setup is ready for SYOS.
    echo.
    echo You can now run:
    echo   run-mysql.bat
    echo   or
    echo   run.bat
) else (
    echo ❌ %ERROR_COUNT% ISSUES FOUND
    echo Please fix the issues above before running SYOS.
    echo.
    echo For help, check:
    echo   - MYSQL-COMPLETE-SETUP-GUIDE.md
    echo   - MYSQL-TROUBLESHOOTING.md
    echo   - Run quick-mysql-setup.bat for automated setup
)
echo.
echo ========================================
pause
