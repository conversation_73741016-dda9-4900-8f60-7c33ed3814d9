package com.syos.model;

/**
 * Represents a user in the system.
 * Users are required for online purchases.
 */
public class User {
    private String userId;
    private String username;
    private String password; // In a real system, this would be hashed
    private String fullName;
    private String email;
    private String address;
    private String phone;
    private boolean isOnlineCustomer;

    public User() {
    }

    public User(String userId, String username, String password, String fullName,
            String email, String address, String phone, boolean isOnlineCustomer) {
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("User ID cannot be null or empty");
        }
        if (!isValidEmail(email)) {
            throw new IllegalArgumentException("Invalid email format: " + email);
        }
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.email = email;
        this.address = address;
        this.phone = phone;
        this.isOnlineCustomer = isOnlineCustomer;
    }

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public boolean isOnlineCustomer() {
        return isOnlineCustomer;
    }

    public void setOnlineCustomer(boolean onlineCustomer) {
        isOnlineCustomer = onlineCustomer;
    }

    @Override
    public String toString() {
        return "User{" +
                "userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", address='" + address + '\'' +
                ", phone='" + phone + '\'' +
                ", isOnlineCustomer=" + isOnlineCustomer +
                '}';
    }

    // Validation methods
    public boolean validatePassword(String inputPassword) {
        return this.password != null && this.password.equals(inputPassword);
    }

    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        // Simple email validation - contains @ and has text before and after
        return email.contains("@") &&
                email.indexOf("@") > 0 &&
                email.indexOf("@") < email.length() - 1 &&
                email.indexOf("@") == email.lastIndexOf("@");
    }

    // Getter method for ID (alias for getUserId for consistency)
    public String getId() {
        return userId;
    }
}
