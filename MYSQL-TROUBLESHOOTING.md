# MySQL Troubleshooting Guide for SYOS

## Common Issues and Solutions

### 1. "Access denied for user 'root'@'localhost'"

**Symptoms:**
- Application shows MySQL connection errors
- Cannot connect to MySQL from command line

**Solutions:**

#### Option A: Reset Root Password
```cmd
# Stop MySQL service
net stop mysql80

# Start MySQL in safe mode (open new admin cmd)
mysqld --skip-grant-tables --skip-networking

# In another command prompt
mysql -u root
```
```sql
USE mysql;
UPDATE user SET authentication_string=PASSWORD('root') WHERE User='root';
FLUSH PRIVILEGES;
exit;
```
```cmd
# Stop safe mode MySQL and restart service
net start mysql80
```

#### Option B: Use Correct Password
1. Check what password you set during MySQL installation
2. Update `.env` file with correct password:
   ```
   DB_PASSWORD=your_actual_password
   ```

### 2. "Can't connect to MySQL server on 'localhost'"

**Symptoms:**
- Connection timeout errors
- "Connection refused" messages

**Solutions:**

#### Check MySQL Service Status
```cmd
# Check if MySQL is running
net start | findstr MySQL

# Start MySQL service if not running
net start mysql80
```

#### Verify MySQL Installation
```cmd
# Check MySQL version
mysql --version

# Check if MySQL is listening on port 3306
netstat -an | findstr 3306
```

#### Check Windows Firewall
1. Open Windows Defender Firewall
2. Allow MySQL through firewall (port 3306)

### 3. "Unknown database 'syos_db'"

**Symptoms:**
- Database connection works but can't find syos_db

**Solutions:**

#### Create Database Manually
```cmd
mysql -u root -p
```
```sql
CREATE DATABASE syos_db;
SHOW DATABASES;
exit;
```

#### Run Setup Scripts
```cmd
# Run database setup
mysql -u root -p syos_db < database\setup.sql
```

### 4. "Table doesn't exist" Errors

**Symptoms:**
- Database connects but tables are missing
- SQL errors about missing tables

**Solutions:**

#### Check Existing Tables
```cmd
mysql -u root -p syos_db -e "SHOW TABLES;"
```

#### Recreate Tables
```cmd
# Run setup script again
mysql -u root -p syos_db < database\setup.sql
```

#### Manual Table Creation
```sql
USE syos_db;
SOURCE database/setup.sql;
```

### 5. ".env File Not Found" Warning

**Symptoms:**
- Application shows "No .env file found" message
- Uses default configuration

**Solutions:**

#### Create .env File
1. Copy the provided `.env` template
2. Update with your MySQL credentials:
   ```
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=syos_db
   DB_USERNAME=root
   DB_PASSWORD=your_password
   ```

### 6. "MySQL JDBC Driver Not Found"

**Symptoms:**
- ClassNotFoundException for MySQL driver
- "No suitable driver found" errors

**Solutions:**

#### Check JDBC Driver
1. Verify `mysql-connector-java-8.0.33.jar` exists in `lib/` folder
2. Check classpath in run scripts includes the JAR file

#### Download JDBC Driver
If missing, download from:
https://dev.mysql.com/downloads/connector/j/

### 7. Application Runs But Uses CSV Files

**Symptoms:**
- No MySQL errors but data isn't persisted to database
- Application works but changes don't survive restart

**Solutions:**

#### Check Configuration Priority
1. Verify `.env` file has correct settings
2. Check no system properties override .env settings
3. Restart application after changing .env

#### Verify Database Connection
```java
// Add this to test connection
System.out.println("Using database: " + DatabaseConfig.getDatabaseName());
System.out.println("Username: " + DatabaseConfig.getUsername());
```

### 8. Performance Issues

**Symptoms:**
- Slow database operations
- Connection timeouts

**Solutions:**

#### Optimize MySQL Configuration
Add to `.env`:
```
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=60000
USE_SERVER_PREP_STMTS=true
CACHE_PREP_STMTS=true
```

#### Check MySQL Configuration
```sql
SHOW VARIABLES LIKE 'max_connections';
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
```

## Diagnostic Commands

### Check MySQL Status
```cmd
# Service status
sc query mysql80

# Process status
tasklist | findstr mysql

# Port status
netstat -an | findstr 3306
```

### Test Database Connection
```cmd
# Basic connection test
mysql -u root -p -e "SELECT NOW();"

# Test specific database
mysql -u root -p syos_db -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='syos_db';"
```

### Check SYOS Configuration
```cmd
# Show current configuration
java -cp "lib/*;." com.syos.config.DatabaseConfig
```

## Getting Help

### Log Files
- MySQL Error Log: Usually in MySQL installation directory
- SYOS Application: Check console output for error messages

### Useful MySQL Commands
```sql
-- Show all databases
SHOW DATABASES;

-- Show tables in current database
SHOW TABLES;

-- Show table structure
DESCRIBE table_name;

-- Show current user and host
SELECT USER(), @@hostname;

-- Show MySQL version
SELECT VERSION();
```

### Contact Information
- Check project README.md for additional help
- Review MySQL official documentation
- Check SYOS project documentation files

## Prevention Tips

1. **Regular Backups**: Backup your database regularly
2. **Document Changes**: Keep track of configuration changes
3. **Test Environment**: Use a separate test database for development
4. **Monitor Logs**: Regularly check MySQL and application logs
5. **Update Regularly**: Keep MySQL and JDBC drivers updated
