package com.syos.service;

import com.syos.model.User;
import com.syos.repository.UserRepository;

import java.util.List;
import java.util.Optional;

/**
 * Service class for user management operations.
 */
public class UserService {

    private final UserRepository userRepository;

    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * Registers a new user.
     * 
     * @param user The user to register
     * @return The registered user
     * @throws IllegalArgumentException if the username is already taken
     */
    public User registerUser(User user) {
        if (userRepository.findByUsername(user.getUsername()).isPresent()) {
            throw new IllegalArgumentException("Username already taken");
        }

        return userRepository.save(user);
    }

    /**
     * Authenticates a user.
     * 
     * @param username The username
     * @param password The password
     * @return An Optional containing the authenticated user if successful, empty
     *         otherwise
     */
    public Optional<User> authenticateUser(String username, String password) {
        return userRepository.authenticate(username, password);
    }

    /**
     * Gets a user by their ID.
     * 
     * @param userId The user ID
     * @return An Optional containing the user if found, empty otherwise
     */
    public Optional<User> getUserById(String userId) {
        return userRepository.findById(userId);
    }

    /**
     * Gets a user by their username.
     * 
     * @param username The username
     * @return An Optional containing the user if found, empty otherwise
     */
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * Gets all users.
     * 
     * @return A list of all users
     */
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    /**
     * Updates a user's information.
     * 
     * @param user The user to update
     * @return The updated user
     */
    public User updateUser(User user) {
        return userRepository.update(user);
    }

    /**
     * Deletes a user.
     * 
     * @param userId The ID of the user to delete
     * @return true if deleted, false otherwise
     */
    public boolean deleteUser(String userId) {
        return userRepository.delete(userId);
    }
}
