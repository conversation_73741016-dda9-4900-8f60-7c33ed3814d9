package com.syos.repository.impl;

import com.syos.model.Item;
import com.syos.model.StockBatch;
import com.syos.model.StockType;
import com.syos.repository.StockRepository;
import com.syos.util.DatabaseUtil;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MySQL implementation of StockRepository.
 */
public class MySqlStockRepository implements StockRepository {
    
    @Override
    public StockBatch save(StockBatch stockBatch) {
        String sql = """
            INSERT INTO stock_batches (batch_id, item_code, quantity, received_date, expiry_date, stock_type) 
            VALUES (?, ?, ?, ?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            quantity = VALUES(quantity), 
            received_date = VALUES(received_date), 
            expiry_date = VALUES(expiry_date), 
            stock_type = VALUES(stock_type)
        """;
        
        try (Connection conn = DatabaseUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, stockBatch.getBatchId());
            stmt.setString(2, stockBatch.getItem().getCode());
            stmt.setInt(3, stockBatch.getQuantity());
            stmt.setDate(4, Date.valueOf(stockBatch.getReceivedDate()));
            stmt.setDate(5, Date.valueOf(stockBatch.getExpiryDate()));
            stmt.setString(6, stockBatch.getStockType().toString());
            
            stmt.executeUpdate();
            return stockBatch;
            
        } catch (SQLException e) {
            System.err.println("Error saving stock batch: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    public StockBatch update(StockBatch stockBatch) {
        String sql = """
            UPDATE stock_batches 
            SET quantity = ?, received_date = ?, expiry_date = ?, stock_type = ? 
            WHERE batch_id = ?
        """;
        
        try (Connection conn = DatabaseUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, stockBatch.getQuantity());
            stmt.setDate(2, Date.valueOf(stockBatch.getReceivedDate()));
            stmt.setDate(3, Date.valueOf(stockBatch.getExpiryDate()));
            stmt.setString(4, stockBatch.getStockType().toString());
            stmt.setString(5, stockBatch.getBatchId());
            
            stmt.executeUpdate();
            return stockBatch;
            
        } catch (SQLException e) {
            System.err.println("Error updating stock batch: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    public Optional<StockBatch> findByBatchId(String batchId) {
        String sql = """
            SELECT sb.*, i.name, i.price, i.category 
            FROM stock_batches sb 
            JOIN items i ON sb.item_code = i.code 
            WHERE sb.batch_id = ?
        """;
        
        try (Connection conn = DatabaseUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, batchId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return Optional.of(createStockBatchFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Error finding stock batch by ID: " + e.getMessage());
            e.printStackTrace();
        }
        
        return Optional.empty();
    }
    
    @Override
    public List<StockBatch> findByItemCode(String itemCode) {
        List<StockBatch> batches = new ArrayList<>();
        String sql = """
            SELECT sb.*, i.name, i.price, i.category 
            FROM stock_batches sb 
            JOIN items i ON sb.item_code = i.code 
            WHERE sb.item_code = ? 
            ORDER BY sb.expiry_date ASC
        """;
        
        try (Connection conn = DatabaseUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, itemCode);
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                batches.add(createStockBatchFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Error finding stock batches by item code: " + e.getMessage());
            e.printStackTrace();
        }
        
        return batches;
    }
    
    @Override
    public List<StockBatch> findByStockType(StockType stockType) {
        List<StockBatch> batches = new ArrayList<>();
        String sql = """
            SELECT sb.*, i.name, i.price, i.category 
            FROM stock_batches sb 
            JOIN items i ON sb.item_code = i.code 
            WHERE sb.stock_type = ? 
            ORDER BY sb.expiry_date ASC
        """;
        
        try (Connection conn = DatabaseUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, stockType.toString());
            ResultSet rs = stmt.executeQuery();
            
            while (rs.next()) {
                batches.add(createStockBatchFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Error finding stock batches by type: " + e.getMessage());
            e.printStackTrace();
        }
        
        return batches;
    }
    
    @Override
    public List<StockBatch> findAll() {
        List<StockBatch> batches = new ArrayList<>();
        String sql = """
            SELECT sb.*, i.name, i.price, i.category 
            FROM stock_batches sb 
            JOIN items i ON sb.item_code = i.code 
            ORDER BY sb.expiry_date ASC
        """;
        
        try (Connection conn = DatabaseUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                batches.add(createStockBatchFromResultSet(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("Error finding all stock batches: " + e.getMessage());
            e.printStackTrace();
        }
        
        return batches;
    }
    
    @Override
    public int getTotalQuantity(String itemCode, StockType stockType) {
        String sql = "SELECT SUM(quantity) FROM stock_batches WHERE item_code = ? AND stock_type = ?";
        
        try (Connection conn = DatabaseUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, itemCode);
            stmt.setString(2, stockType.toString());
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1);
            }
            
        } catch (SQLException e) {
            System.err.println("Error getting total quantity: " + e.getMessage());
            e.printStackTrace();
        }
        
        return 0;
    }
    
    @Override
    public boolean delete(String batchId) {
        String sql = "DELETE FROM stock_batches WHERE batch_id = ?";
        
        try (Connection conn = DatabaseUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, batchId);
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("Error deleting stock batch: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Creates a StockBatch object from a ResultSet.
     */
    private StockBatch createStockBatchFromResultSet(ResultSet rs) throws SQLException {
        Item item = new Item(
            rs.getString("item_code"),
            rs.getString("name"),
            rs.getDouble("price"),
            rs.getString("category")
        );
        
        return new StockBatch(
            rs.getString("batch_id"),
            item,
            rs.getInt("quantity"),
            rs.getDate("received_date").toLocalDate(),
            rs.getDate("expiry_date").toLocalDate(),
            StockType.valueOf(rs.getString("stock_type"))
        );
    }
}
