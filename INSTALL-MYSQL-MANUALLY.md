# Manual MySQL Installation Guide

Since the automated installation is taking time, here's how to install MySQL manually:

## Option 1: Download MySQL Installer (Recommended)

1. **Download MySQL Installer**:
   - Go to: https://dev.mysql.com/downloads/installer/
   - Download "mysql-installer-community-********.msi" (about 350MB)
   - Choose "No thanks, just start my download"

2. **Run the Installer**:
   - Double-click the downloaded .msi file
   - Choose "Developer Default" setup type
   - Click "Next" through the installation
   - Set a root password when prompted (remember this!)
   - Complete the installation

3. **Verify Installation**:
   ```bash
   mysql --version
   ```

## Option 2: Download MySQL ZIP Archive

1. **Download MySQL ZIP**:
   - Go to: https://dev.mysql.com/downloads/mysql/
   - Select "Windows (x86, 64-bit), ZIP Archive"
   - Download the ZIP file

2. **Extract and Setup**:
   - Extract to `C:\mysql`
   - Add `C:\mysql\bin` to your PATH environment variable
   - Create `my.ini` configuration file in `C:\mysql`

3. **Initialize and Start**:
   ```bash
   mysqld --initialize-insecure
   mysqld --install
   net start mysql
   ```

## Option 3: Use Chocolatey (if available)

```bash
choco install mysql
```

## Option 4: Use Scoop (if available)

```bash
scoop install mysql
```

## After Installation

1. **Test MySQL Connection**:
   ```bash
   mysql -u root -p
   ```

2. **Set Root Password** (if not set during installation):
   ```sql
   ALTER USER 'root'@'localhost' IDENTIFIED BY 'your_password';
   ```

3. **Create SYOS Database**:
   ```bash
   mysql -u root -p < database/setup.sql
   ```

4. **Load Sample Data**:
   ```bash
   mysql -u root -p < database/sample_data.sql
   ```

## Quick Test

Once MySQL is installed, test with:
```bash
mysql --version
mysql -u root -p -e "SELECT 'MySQL is working!' as status;"
```

## Next Steps

After MySQL is installed:
1. Run `setup-mysql.bat` to create the database
2. Run `run.bat` to start the SYOS application
3. Login with: admin/admin123 or customer/customer123

## Troubleshooting

- **MySQL service not starting**: Check Windows Services and start "MySQL80" service
- **Access denied**: Verify your root password
- **Command not found**: Add MySQL bin directory to PATH
- **Port 3306 in use**: Stop other MySQL instances or change port

The SYOS application will automatically fall back to file storage if MySQL is not available.
