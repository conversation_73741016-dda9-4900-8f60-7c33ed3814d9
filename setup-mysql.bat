@echo off
echo ========================================
echo SYOS MySQL Database Setup
echo ========================================

echo.
echo This script will help you set up the MySQL database for SYOS.
echo.

echo Step 1: Checking MySQL installation...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: MySQL is not installed or not in PATH.
    echo Please install MySQL Community Server from:
    echo https://dev.mysql.com/downloads/mysql/
    echo.
    pause
    exit /b 1
)
echo MySQL is installed.

echo.
echo Step 2: Setting up database...
echo Please enter your MySQL root password when prompted.
echo.

mysql -u root -p -e "source database/setup.sql"
if %errorlevel% neq 0 (
    echo ERROR: Failed to create database.
    echo Please check your MySQL credentials and try again.
    pause
    exit /b 1
)

echo.
echo Step 3: Loading sample data (optional)...
set /p load_sample="Do you want to load sample data? (y/n): "
if /i "%load_sample%"=="y" (
    mysql -u root -p -e "source database/sample_data.sql"
    if %errorlevel% neq 0 (
        echo WARNING: Failed to load sample data.
        echo You can load it manually later.
    ) else (
        echo Sample data loaded successfully.
    )
)

echo.
echo ========================================
echo Database setup complete!
echo ========================================
echo.
echo You can now run the SYOS application with:
echo   run-mysql.bat
echo.
echo Or simply:
echo   java -cp "bin;lib/mysql-connector-java-5.1.49.jar" com.syos.SyosApplication
echo.
pause
