package com.syos.factory;

import com.syos.service.*;

/**
 * Factory class for creating service instances with proper repository dependencies.
 */
public class ServiceFactory {
    
    private static ServiceFactory instance;
    private final RepositoryFactory repositoryFactory;
    
    // Service instances (singleton pattern)
    private InventoryService inventoryService;
    private BillingService billingService;
    private ReportService reportService;
    private UserService userService;
    
    private ServiceFactory(RepositoryFactory repositoryFactory) {
        this.repositoryFactory = repositoryFactory;
    }
    
    /**
     * Gets the singleton instance of the service factory.
     * 
     * @param repositoryFactory The repository factory to use
     * @return The service factory instance
     */
    public static ServiceFactory getInstance(RepositoryFactory repositoryFactory) {
        if (instance == null) {
            instance = new ServiceFactory(repositoryFactory);
        }
        return instance;
    }
    
    /**
     * Gets the current instance (must be initialized first).
     * 
     * @return The service factory instance
     */
    public static ServiceFactory getInstance() {
        if (instance == null) {
            throw new IllegalStateException("ServiceFactory not initialized. Call getInstance(RepositoryFactory) first.");
        }
        return instance;
    }
    
    /**
     * Gets an InventoryService instance.
     * 
     * @return The InventoryService
     */
    public InventoryService getInventoryService() {
        if (inventoryService == null) {
            inventoryService = new InventoryService(
                repositoryFactory.getItemRepository(),
                repositoryFactory.getStockRepository()
            );
        }
        return inventoryService;
    }
    
    /**
     * Gets a BillingService instance.
     * 
     * @return The BillingService
     */
    public BillingService getBillingService() {
        if (billingService == null) {
            billingService = new BillingService(
                repositoryFactory.getItemRepository(),
                repositoryFactory.getTransactionRepository(),
                repositoryFactory.getBillRepository(),
                getInventoryService()
            );
        }
        return billingService;
    }
    
    /**
     * Gets a ReportService instance.
     * 
     * @return The ReportService
     */
    public ReportService getReportService() {
        if (reportService == null) {
            reportService = new ReportService(
                repositoryFactory.getItemRepository(),
                repositoryFactory.getStockRepository(),
                repositoryFactory.getTransactionRepository(),
                repositoryFactory.getBillRepository(),
                getInventoryService()
            );
        }
        return reportService;
    }
    
    /**
     * Gets a UserService instance.
     * 
     * @return The UserService
     */
    public UserService getUserService() {
        if (userService == null) {
            userService = new UserService(repositoryFactory.getUserRepository());
        }
        return userService;
    }
    
    /**
     * Resets all service instances (useful for testing or switching storage types).
     */
    public static void reset() {
        instance = null;
    }
}
