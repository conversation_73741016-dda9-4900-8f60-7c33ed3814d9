package com.syos.util;

import com.syos.config.DatabaseConfig;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * Database utility class for managing database connections and operations.
 */
public class DatabaseUtil {
    
    private static Connection connection;
    
    /**
     * Gets a database connection. Creates a new connection if one doesn't exist.
     * 
     * @return The database connection
     * @throws SQLException If connection fails
     */
    public static Connection getConnection() throws SQLException {
        if (connection == null || connection.isClosed()) {
            try {
                // Load MySQL JDBC driver
                Class.forName("com.mysql.cj.jdbc.Driver");
                
                // Create connection
                connection = DriverManager.getConnection(
                        DatabaseConfig.getJdbcUrl(),
                        DatabaseConfig.getUsername(),
                        DatabaseConfig.getPassword()
                );
                
                System.out.println("Database connection established successfully.");
            } catch (ClassNotFoundException e) {
                throw new SQLException("MySQL JDBC driver not found", e);
            }
        }
        return connection;
    }
    
    /**
     * Closes the database connection.
     */
    public static void closeConnection() {
        if (connection != null) {
            try {
                connection.close();
                System.out.println("Database connection closed.");
            } catch (SQLException e) {
                System.err.println("Error closing database connection: " + e.getMessage());
            }
        }
    }
    
    /**
     * Initializes the database by creating tables if they don't exist.
     */
    public static void initializeDatabase() {
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            
            // Create database if it doesn't exist
            createDatabaseIfNotExists();
            
            // Create tables
            createTables(stmt);
            
            System.out.println("Database initialized successfully.");
            
        } catch (SQLException e) {
            System.err.println("Error initializing database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Creates the database if it doesn't exist.
     */
    private static void createDatabaseIfNotExists() throws SQLException {
        String jdbcUrlWithoutDb = String.format("*******************************************************************************",
                DatabaseConfig.getHost(), DatabaseConfig.getPort());
        
        try (Connection conn = DriverManager.getConnection(jdbcUrlWithoutDb, 
                DatabaseConfig.getUsername(), DatabaseConfig.getPassword());
             Statement stmt = conn.createStatement()) {
            
            String createDbSql = "CREATE DATABASE IF NOT EXISTS " + DatabaseConfig.getDatabaseName() + 
                               " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            stmt.executeUpdate(createDbSql);
            
        } catch (SQLException e) {
            System.err.println("Error creating database: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * Creates all necessary tables.
     */
    private static void createTables(Statement stmt) throws SQLException {
        // Create items table
        String createItemsTable = """
            CREATE TABLE IF NOT EXISTS items (
                code VARCHAR(50) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                price DECIMAL(10, 2) NOT NULL,
                category VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        """;
        stmt.executeUpdate(createItemsTable);
        
        // Create users table
        String createUsersTable = """
            CREATE TABLE IF NOT EXISTS users (
                user_id VARCHAR(50) PRIMARY KEY,
                username VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                address TEXT,
                phone VARCHAR(20),
                is_online_customer BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        """;
        stmt.executeUpdate(createUsersTable);
        
        // Create stock_batches table
        String createStockBatchesTable = """
            CREATE TABLE IF NOT EXISTS stock_batches (
                batch_id VARCHAR(50) PRIMARY KEY,
                item_code VARCHAR(50) NOT NULL,
                quantity INT NOT NULL DEFAULT 0,
                received_date DATE NOT NULL,
                expiry_date DATE NOT NULL,
                stock_type ENUM('SHELF', 'STORAGE', 'ONLINE') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (item_code) REFERENCES items(code) ON DELETE CASCADE
            )
        """;
        stmt.executeUpdate(createStockBatchesTable);
        
        // Create transactions table
        String createTransactionsTable = """
            CREATE TABLE IF NOT EXISTS transactions (
                transaction_id VARCHAR(50) PRIMARY KEY,
                user_id VARCHAR(50),
                transaction_type ENUM('IN_STORE', 'ONLINE') NOT NULL,
                total_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
                discount_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
                final_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
                cash_tendered DECIMAL(10, 2) DEFAULT 0.00,
                change_amount DECIMAL(10, 2) DEFAULT 0.00,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
            )
        """;
        stmt.executeUpdate(createTransactionsTable);
        
        // Create transaction_items table (for cart items)
        String createTransactionItemsTable = """
            CREATE TABLE IF NOT EXISTS transaction_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                transaction_id VARCHAR(50) NOT NULL,
                item_code VARCHAR(50) NOT NULL,
                quantity INT NOT NULL,
                unit_price DECIMAL(10, 2) NOT NULL,
                subtotal DECIMAL(10, 2) NOT NULL,
                FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE CASCADE,
                FOREIGN KEY (item_code) REFERENCES items(code) ON DELETE CASCADE
            )
        """;
        stmt.executeUpdate(createTransactionItemsTable);
        
        // Create bills table
        String createBillsTable = """
            CREATE TABLE IF NOT EXISTS bills (
                bill_number VARCHAR(50) PRIMARY KEY,
                transaction_id VARCHAR(50) NOT NULL,
                generated_date_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE CASCADE
            )
        """;
        stmt.executeUpdate(createBillsTable);
        
        System.out.println("All tables created successfully.");
    }
}
