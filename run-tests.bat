@echo off

echo ========================================
echo SYOS Test Suite Runner
echo ========================================

REM Check if JUnit is available
if not exist "lib\junit-4.13.2.jar" (
    echo ERROR: JUnit library not found!
    echo Please download JUnit 4.13.2 and place it in the lib directory.
    echo Download from: https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar
    pause
    exit /b 1
)

if not exist "lib\hamcrest-core-1.3.jar" (
    echo ERROR: Hamcrest library not found!
    echo Please download Hamcrest Core 1.3 and place it in the lib directory.
    echo Download from: https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar
    pause
    exit /b 1
)

REM Create bin directory if it doesn't exist
if not exist bin mkdir bin

echo Compiling main application...
javac -d bin -cp "lib\mysql-connector-java-5.1.49.jar" com\syos\*.java com\syos\*\*.java com\syos\*\*\*.java

if %errorlevel% neq 0 (
    echo ERROR: Main application compilation failed!
    pause
    exit /b 1
)

echo Compiling test classes...
javac -d bin -cp "bin;lib\junit-4.13.2.jar;lib\hamcrest-core-1.3.jar;lib\mysql-connector-java-5.1.49.jar" com\syos\test\*.java com\syos\test\*\*.java

if %errorlevel% neq 0 (
    echo ERROR: Test compilation failed!
    pause
    exit /b 1
)

echo Running test suite...
java -cp "bin;lib\junit-4.13.2.jar;lib\hamcrest-core-1.3.jar;lib\mysql-connector-java-5.1.49.jar" org.junit.runner.JUnitCore com.syos.test.SyosTestSuite

echo ========================================
echo Test execution completed!
echo ========================================
pause
